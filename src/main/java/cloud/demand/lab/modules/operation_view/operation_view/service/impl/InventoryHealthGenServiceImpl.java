package cloud.demand.lab.modules.operation_view.operation_view.service.impl;

import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpecialDateUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticGinsfamilyDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.entity.plan.StaticCvmtypeDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.BufferAverageCoreDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryBufferPoolConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory.SafetyInventoryFutureOtherDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory.SafetyInventoryHistoryDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory.SafetyInventoryHistoryPeakDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.ActualDeliverySlaDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CRSDuandaoduanServiceLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CbsServiceLevelProcessedRawDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.ClsLogServiceLevelDiDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CynosApplysetInfoDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CynosApplysetSuccessDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CynosdbDuandaoduanServiceLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CynosstockinfoDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DuandaoduanServiceLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCbsServiceLevelDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCdbServiceLevelDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCrsServiceLevelDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsEndToEndZoneDeviceModelMonthDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthMonthDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeekDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeeklyDeliveryDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeeklyScaleDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsLeisureAndBusySoldOutDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsTdsqlServiceLevelDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthBufferConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthZlkhbSafetyInventoryDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthZlkhbSafetyInventorySnapshotDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.CustomerCustomGroupEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryHealthActualV2ServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryHealthConfigServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryHealthDictServiceImpl;
import cloud.demand.lab.modules.operation_view.operation_view.entity.AppidAndNameDTO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DemandWeekNConfigDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsApiSuccessDataDfLocalDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsDemandWeekNPplVersionItemDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsInventoryHealthMckForecastMonthlySafeDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsInventoryHealthMckForecastTurnoverDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsInventoryHealthMckTurnoverWfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsInventoryHealthSupplySummaryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsSafeInventoryHistoryMonthlyDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsSoldOutDataDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.EMRData.Item;
import cloud.demand.lab.modules.operation_view.operation_view.entity.EMRJson;
import cloud.demand.lab.modules.operation_view.operation_view.entity.GenActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.GenMckForecastMonthlySafeInvDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.GenSupplySummaryActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.GenSupplySummaryDeliveryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.PplVersionItemDO;
import cloud.demand.lab.modules.operation_view.operation_view.enums.ClsLogProductEnum;
import cloud.demand.lab.modules.operation_view.operation_view.model.ClsLogInfo;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2;
import cloud.demand.lab.modules.operation_view.operation_view.service.ClsLogService;
import cloud.demand.lab.modules.operation_view.operation_view.service.InventoryHealthGenService;
import cloud.demand.lab.modules.operation_view.operation_view.service.OutsideViewService;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OperationViewService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.impl.OperationViewServiceImpl;
import cloud.demand.lab.modules.operation_view.operation_view_old.utils.OperationViewTools;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import report.utils.utils.CkDBUtils;
import report.utils.utils.ReportDateUtils;
import yunti.boot.exception.BizException;
import cloud.demand.lab.common.config.DBList;

@Service
@Slf4j
public class InventoryHealthGenServiceImpl implements InventoryHealthGenService {

    @Resource
    private InventoryHealthDictService inventoryHealthDictService;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService baseDictService;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private DBHelper ckcubesDBHelper;

    @Resource
    private DBHelper arch2DBHelper;

    @Resource
    private DBHelper planDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private DBHelper cbshawkeyeDBHelper;

    @Resource
    private DBHelper yuntiDBHelper;

    @Resource
    private DBHelper tdsqlresourceDBHelper;

    @Resource
    DictService dictService;

    @Resource
    OutsideViewService outsideViewService;

    @Resource
    private DBHelper planReportDBHelper;

    @Resource
    private DBHelper dialsystemDBHelper;


    @Resource
    private OutsideViewOldService outsideViewOldService;

    /** cls日志服务 */
    @Resource
    private ClsLogService clsLogService;

    /**
     * 历史算法周跨度，默认前13周
     */
    private final Integer historyWeekSpanNum = -13;

    /**
     * 未来算法的周跨度，默认未来13周
     */
    private final Integer futureWeekSpanNum = 13;

    private final String emrUrl = "https://prom-efxjoxw7.grafana.sh-vip.prom.tencent-cloud.com/api/datasources/proxy/1/api/v1/query";


    @Override
    @TaskLog(taskName = "genSafetyInventoryDetail")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genSafetyInventoryDetail(String statTime, List<String> uins, List<String> customerNames, Boolean isPositive) {
        if (StringTools.isBlank(statTime)){
            return;
        }

        if (ListUtils.isEmpty(uins) && ListUtils.isEmpty(customerNames)) {
            //  清理当天已经生成的数据，保证幂等
            String cleanSql =
                    "ALTER TABLE cloud_demand.dws_inventory_health_weekly_scale_df_local ON CLUSTER default_cluster DELETE " +
                            "where stat_time = ? and exclude_uin_list = '(空值)'";
            ckcldDBHelper.executeRaw(cleanSql, statTime);
        }

        //  历史周峰 + 历史周均
        genHistoryPartData(statTime, uins, customerNames, isPositive, historyWeekSpanNum);
        //  未来周峰
        genFuturePartData(statTime, futureWeekSpanNum);
    }

    @Override
    public void genSafetyInventoryDataHistoryPeak(String statTime, List<String> uins, List<String> customerNames, Boolean isPositive) {
        if (StringTools.isBlank(statTime)){
            return;
        }

        if (ListUtils.isEmpty(uins) && ListUtils.isEmpty(customerNames)) {
            //  清理当天已经生成的历史算法数据，保证幂等
            String cleanSql =
                    "ALTER TABLE cloud_demand.dws_inventory_health_weekly_scale_df_local ON CLUSTER default_cluster DELETE " +
                            "where stat_time = ? and exclude_uin_list = '(空值)' and week_index < 0";
            ckcldDBHelper.executeRaw(cleanSql, statTime);
        }

        //  历史周峰 + 历史周均
        genHistoryPartData(statTime, uins, customerNames, isPositive, historyWeekSpanNum);
    }

    @Override
    @TaskLog(taskName = "genDeliveryDaysData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genDeliveryDaysData(String statTime) {
        // 清理当天的数据，保证幂等
        ckcldDBHelper.executeRaw(
                "ALTER TABLE cloud_demand.dws_inventory_health_weekly_delivery_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);

        //  1、获取历史指定时间区间范围的节假周信息
        LocalDate date = LocalDate.parse(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(date, historyWeekSpanNum);
        if (ListUtils.isEmpty(holidayWeekInfo)) {
            return;
        }
        //  获取第一周周一，作为beginDate
        String beginDate = holidayWeekInfo.get(0).getStartDate();
        //  获取最后一周周日，作为endDate
        String lastDate = holidayWeekInfo.get(holidayWeekInfo.size() - 1).getEndDate();

        // 2、获取交付周期数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/demand_market_delivery_sla.sql");
        List<ActualDeliverySlaDO> deliverySlaDOS = ckcubesDBHelper.getRaw(ActualDeliverySlaDO.class, sql, statTime, beginDate, lastDate);
        Map<String, StaticZoneDO> staticZoneDOMap = SpringUtil.getBean(DictServiceImpl.class).getCampus2ZoneInfoMap();
        List<DwsInventoryHealthWeeklyDeliveryDfDO> inventoryHealthWeeklyDeliveryDfDOS = deliverySlaDOS.stream().map(sla -> {
            DwsInventoryHealthWeeklyDeliveryDfDO deliveryDfDO = sla.transform();
            deliveryDfDO.setStatTime(statTime);

            // 设置节假周信息
            LocalDateTime erqActualDate = deliveryDfDO.getErpActualDate();
            for (HolidayWeekInfoDTO info : holidayWeekInfo) {
                LocalDateTime weekStartDate = DateUtils.parseLocalDateTime(info.getStartDate());
                LocalDateTime weekEndDate = DateUtils.parseLocalDateTime(info.getEndDate());

                if ((erqActualDate.isAfter(weekStartDate) || erqActualDate.isEqual(weekStartDate)) && (erqActualDate.isBefore(weekEndDate) || erqActualDate.isEqual(weekEndDate))) {
                    deliveryDfDO.setHolidayYear(info.getYear());
                    deliveryDfDO.setHolidayMonth(info.getMonth());
                    deliveryDfDO.setHolidayWeek(info.getWeek());
                    deliveryDfDO.setHolidayWeekStartDate(info.getStartDate());
                    deliveryDfDO.setHolidayWeekEndDate(info.getEndDate());
                    deliveryDfDO.setWeekIndex(info.getWeekNFromNow());
                    break;
                }
            }

            // 根据映射设置机型
            String deviceType = deliveryDfDO.getDeviceType();
            String instanceType = SpringUtil.getBean(DictServiceImpl.class).getCsigInstanceTypeByDeviceType(deviceType);
            deliveryDfDO.setInstanceType(instanceType);

            // 根据映射设置地域信息
            String campus = deliveryDfDO.getCampus();
            StaticZoneDO zoneInfo = staticZoneDOMap.get(campus);

            if (zoneInfo != null) {
                deliveryDfDO.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                deliveryDfDO.setAreaName(zoneInfo.getAreaName());
                deliveryDfDO.setRegionName(zoneInfo.getRegionName());
                deliveryDfDO.setZoneName(zoneInfo.getZoneName());
            }

            // 设置 SLA
            if (deliveryDfDO.getDeliveryStatus().equals("如期交付")) {
                int configuredSla = SpringUtil.getBean(OperationViewServiceImpl.class).getSLA(deliveryDfDO.getDeviceType(), deliveryDfDO.getCustomhouseTitle().equals("境内"));
                deliveryDfDO.setDeliveryDays(NumberUtils.min(deliveryDfDO.getDeliveryDays(), BigDecimal.valueOf(configuredSla)));
                deliveryDfDO.setSla(deliveryDfDO.getDeliveryDays().add(deliveryDfDO.getXyApprovalDays()));
            }
            return deliveryDfDO;
        }).collect(Collectors.toList());

        ckcldDBHelper.insertBatchWithoutReturnId(inventoryHealthWeeklyDeliveryDfDOS);
    }

    @Override
    @TaskLog(taskName = "genSupplySummaryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genSupplySummaryData(String statTime) {
        // 清理当天的数据，保证幂等
        ckcldDBHelper.executeRaw(
                "ALTER TABLE cloud_demand.dws_inventory_health_supply_summary_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);
        // 1、获取实际库存信息，取好料 + 5种大核库存细分
        String actualInvSql = "select product_type,\n" +
                "       customhouse_title,\n" +
                "       area_name,\n" +
                "       region_name,\n" +
                "       zone_name,\n" +
                "       instance_type,\n" +
                "       device_type,\n" +
                "       line_type,\n" +
                "       material_type,\n" +
                "       inv_detail_type,\n" +
                "       sum(actual_inv) actual_inv,\n" +
                "       sum(device_num) device_num\n" +
                "from cloud_demand.dws_actual_inventory_df\n" +
                "where stat_time = ?\n" +
                "group by product_type, customhouse_title, area_name, region_name, zone_name, instance_type, device_type, line_type, material_type, inv_detail_type";
        List<GenSupplySummaryActualInventoryDfDO> actualInventoryDfDOS = ckcldDBHelper.getRaw(GenSupplySummaryActualInventoryDfDO.class, actualInvSql, statTime);
        // 2、获取到货信息，取云业务 + 腾讯云CVM，跟库存健康保持一致
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/demand_market_supply_summary_delivery.sql");
        List<GenSupplySummaryDeliveryDfDO> deliveryDfDOS = ckcubesDBHelper.getRaw(GenSupplySummaryDeliveryDfDO.class, sql, statTime);
        // 3、合并两者得到最终结果
        Map<String, StaticZoneDO> staticZoneDOMap = SpringUtil.getBean(DictServiceImpl.class).getCampus2ZoneInfoMap();
        List<DwsInventoryHealthSupplySummaryDfDO> supplySummaryDfDOS = ListUtils.newList();

        for (GenSupplySummaryActualInventoryDfDO actualInventoryDfDO : actualInventoryDfDOS) {
            DwsInventoryHealthSupplySummaryDfDO supplySummaryDfDO = new DwsInventoryHealthSupplySummaryDfDO();
            supplySummaryDfDO.setStatTime(statTime);
            supplySummaryDfDO.setSupplyType("库存");
            supplySummaryDfDO.setProductType(actualInventoryDfDO.getProductType());
            supplySummaryDfDO.setCustomhouseTitle(actualInventoryDfDO.getCustomhouseTitle());
            supplySummaryDfDO.setAreaName(actualInventoryDfDO.getAreaName());
            supplySummaryDfDO.setRegionName(actualInventoryDfDO.getRegionName());
            supplySummaryDfDO.setZoneName(actualInventoryDfDO.getZoneName());
            supplySummaryDfDO.setInstanceType(actualInventoryDfDO.getInstanceType());
            supplySummaryDfDO.setDeviceType(actualInventoryDfDO.getDeviceType());
            supplySummaryDfDO.setCores(actualInventoryDfDO.getActualInv());
            supplySummaryDfDO.setLineType(actualInventoryDfDO.getLineType());
            supplySummaryDfDO.setMaterialType(actualInventoryDfDO.getMaterialType());
            supplySummaryDfDO.setInvDetailType(actualInventoryDfDO.getInvDetailType());
            supplySummaryDfDO.setTotalNum(actualInventoryDfDO.getDeviceNum());

            supplySummaryDfDOS.add(supplySummaryDfDO);
        }

        for (GenSupplySummaryDeliveryDfDO deliveryDfDO : deliveryDfDOS) {
            DwsInventoryHealthSupplySummaryDfDO supplySummaryDfDO = new DwsInventoryHealthSupplySummaryDfDO();
            supplySummaryDfDO.setStatTime(statTime);
            supplySummaryDfDO.setSupplyType("采购");
            supplySummaryDfDO.setProductType(deliveryDfDO.getProductType());

            // 根据映射设置机型
            String deviceType = deliveryDfDO.getDeviceType();
            String instanceType = SpringUtil.getBean(DictServiceImpl.class).getCsigInstanceTypeByDeviceType(deviceType);
            deliveryDfDO.setInstanceType(instanceType);

            // 根据映射设置地域信息
            String campus = deliveryDfDO.getCampusName();
            StaticZoneDO zoneInfo = staticZoneDOMap.get(campus);

            if (zoneInfo != null) {
                deliveryDfDO.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                deliveryDfDO.setAreaName(zoneInfo.getAreaName());
                deliveryDfDO.setRegionName(zoneInfo.getRegionName());
                deliveryDfDO.setZoneName(zoneInfo.getZoneName());
            }

            supplySummaryDfDO.setCustomhouseTitle(deliveryDfDO.getCustomhouseTitle());
            supplySummaryDfDO.setAreaName(deliveryDfDO.getAreaName());
            supplySummaryDfDO.setRegionName(deliveryDfDO.getRegionName());
            supplySummaryDfDO.setZoneName(deliveryDfDO.getZoneName());
            supplySummaryDfDO.setInstanceType(deliveryDfDO.getInstanceType());
            supplySummaryDfDO.setDeviceType(deliveryDfDO.getDeviceType());
            supplySummaryDfDO.setCores(deliveryDfDO.getCores());
            // 下面是采购特有的字段
            supplySummaryDfDO.setSubmitTime(deliveryDfDO.getSubmitTime());
            supplySummaryDfDO.setCityZone(deliveryDfDO.getCityZone());
            supplySummaryDfDO.setCampusName(deliveryDfDO.getCampusName());
            supplySummaryDfDO.setTotalNum(deliveryDfDO.getTotalNum());
            supplySummaryDfDO.setTotalDeliveryTime(deliveryDfDO.getTotalDeliveryTime());
            supplySummaryDfDO.setExpectDeliveryDate(deliveryDfDO.getExpectDeliveryDate());
            supplySummaryDfDO.setPromiseDeliveryTime(deliveryDfDO.getPromiseDeliveryTime());
            supplySummaryDfDO.setLogicCores(deliveryDfDO.getLogicCores());
            supplySummaryDfDO.setProduceStatus(deliveryDfDO.getProduceStatus());
            supplySummaryDfDO.setIndustry(deliveryDfDO.getIndustry());
            supplySummaryDfDO.setCustomerName(deliveryDfDO.getCustomerName());

            // 填充售卖核以及预期到货年、周
            supplySummaryDfDO.setSaleCores(BigDecimal.ZERO); // 暂时填 0，需要再说

            if (supplySummaryDfDO.getExpectDeliveryDate() != null) {
                try {
                    ResPlanHolidayWeekDO curWeek = baseDictService.getHolidayWeekInfoByDate(supplySummaryDfDO.getExpectDeliveryDate());
                    if (curWeek != null) {
                        supplySummaryDfDO.setExpectDeliveryYear(curWeek.getYear());
                        supplySummaryDfDO.setExpectDeliveryMonth(curWeek.getMonth());
                        supplySummaryDfDO.setExpectDeliveryWeek(curWeek.getWeek());
                    }
                } catch (Exception e) {
                    log.error("获取周信息失败" + supplySummaryDfDO.getExpectDeliveryDate() + e);
                }
            }

            if (supplySummaryDfDO.getPromiseDeliveryTime() != null) {
                try {
                    ResPlanHolidayWeekDO curWeek = baseDictService.getHolidayWeekInfoByDate(supplySummaryDfDO.getPromiseDeliveryTime());
                    if (curWeek != null) {
                        supplySummaryDfDO.setPromiseDeliveryYear(curWeek.getYear());
                        supplySummaryDfDO.setPromiseDeliveryMonth(curWeek.getMonth());
                        supplySummaryDfDO.setPromiseDeliveryWeek(curWeek.getWeek());
                    } else {
                        // 统一放到本周 + 13 周
                        ResPlanHolidayWeekDO weekDO = baseDictService.getHolidayWeekInfoByDate(statTime);

                        if (weekDO != null) {
                            Date week13Day = DateUtils.addTime(DateUtils.parse(statTime), Calendar.DATE, 13 * 7);
                            ResPlanHolidayWeekDO week13DO = baseDictService.getHolidayWeekInfoByDate(DateUtils.formatDate(week13Day));
                            supplySummaryDfDO.setPromiseDeliveryYear(week13DO.getYear());
                            supplySummaryDfDO.setPromiseDeliveryMonth(week13DO.getMonth());
                            supplySummaryDfDO.setPromiseDeliveryWeek(week13DO.getWeek());
                        }
                    }
                } catch (Exception e) {
                    Date week13Day = DateUtils.addTime(DateUtils.parse(statTime), Calendar.DATE, 13 * 7);
                    ResPlanHolidayWeekDO week13DO = baseDictService.getHolidayWeekInfoByDate(DateUtils.formatDate(week13Day));
                    supplySummaryDfDO.setPromiseDeliveryYear(week13DO.getYear());
                    supplySummaryDfDO.setPromiseDeliveryMonth(week13DO.getMonth());
                    supplySummaryDfDO.setPromiseDeliveryWeek(week13DO.getWeek());
//                    log.error("获取周信息失败" + supplySummaryDfDO.getPromiseDeliveryTime() + e);
                }
            }

            supplySummaryDfDOS.add(supplySummaryDfDO);
        }

        // 4、数据入库
        ckcldDBHelper.insertBatchWithoutReturnId(supplySummaryDfDOS);
    }

    @Override
    @TaskLog(taskName = "snapshotHeadZlkhbData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void snapshotHeadZlkhbData(String statTime) {

        //  保证幂等性
        long count = demandDBHelper.getCount(InventoryHealthZlkhbSafetyInventorySnapshotDO.class, "where stat_time = ?", statTime);
        if (count > 0){
            demandDBHelper.delete(InventoryHealthZlkhbSafetyInventorySnapshotDO.class, "where stat_time = ?", statTime);
        }

        List<InventoryHealthZlkhbSafetyInventoryDO> all
                = demandDBHelper.getAll(InventoryHealthZlkhbSafetyInventoryDO.class);

        //  自动通过用户配置的可用区名关联境内外、area、region
        ListUtils.forEach(all, o -> {
            if (StringTools.isBlank(o.getZoneName()) || Objects.equals(o.getZoneName(), "(空值)")){
                return;
            }
            StaticZoneDO zoneInfo = baseDictService.getStaticZoneInfoByName(o.getZoneName());
            if (zoneInfo == null){
                return;
            }
            o.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
            o.setAreaName(zoneInfo.getAreaName());
            o.setRegionName(zoneInfo.getRegionName());
        });

        List<InventoryHealthZlkhbSafetyInventorySnapshotDO> result = Lang.list();
        if (ListUtils.isNotEmpty(all)) {
            result.addAll(ListUtils.transform(all, o -> {
                InventoryHealthZlkhbSafetyInventorySnapshotDO snapshotDO =
                        InventoryHealthZlkhbSafetyInventorySnapshotDO.snapshot(o);
                snapshotDO.setStatTime(DateUtils.toLocalDate(DateUtils.parse(statTime)));
                return snapshotDO;
            }));
        }

        if (ListUtils.isEmpty(result) ||
                Objects.equals(NumberUtils.sum(result, o -> o.getSafetyInventory()), BigDecimal.ZERO)){
            String msg = statTime + ":战略客户部独立定制的安全库存值均为0，请注意";
            taskLogService.genRunLog("snapshotHeadZlkhbData", "snapshotHeadZlkhbData", msg);
        }
        demandDBHelper.insertBatchWithoutReturnId(result);
    }

    private List<DwsSafeInventoryHistoryMonthlyDfDO> buildHistoryMonthlySafeInventoryData(String statTime) {
        OperationViewService operationViewService = SpringUtil.getBean(OperationViewServiceImpl.class);
        OperationViewService2Impl operationViewService2Impl = SpringUtil.getBean(OperationViewService2Impl.class);

        WhereSQL condition = new WhereSQL();
        condition.and("stat_time = ?", statTime);
        condition.and("week_index < 0 and product_type = 'CVM'");
        // 历史执行的数据，一天的数据大概 5.5 万条
        List<DwsInventoryHealthWeeklyScaleDfDO> all = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfDO.class, condition.getSQL(), condition.getParams());
        Map<String, List<DwsInventoryHealthWeeklyScaleDfDO>> mapList = ListUtils.toMapList(
                all, o -> toKey(o), o -> o);
        // 交付的数据
        WhereSQL deliveryCondition = new WhereSQL();
        deliveryCondition.and("stat_time = ?", statTime);
        deliveryCondition.and("week_index < 0");
        List<DwsInventoryHealthWeeklyDeliveryDfDO> deliveryDfDOS = ckcldDBHelper.getAll(DwsInventoryHealthWeeklyDeliveryDfDO.class, deliveryCondition.getSQL(), deliveryCondition.getParams());
        Map<String, List<DwsInventoryHealthWeeklyDeliveryDfDO>> deliveryDfMap = ListUtils.toMapList(deliveryDfDOS, o -> o.toKey(), o -> o);

        List<DwsSafeInventoryHistoryMonthlyDfDO> result = ListUtils.newList();

        for (String algorithm : new String[] { "历史周峰", "历史周需求" }) {
            for (Map.Entry<String, List<DwsInventoryHealthWeeklyScaleDfDO>> e : mapList.entrySet()) {
                DwsSafeInventoryHistoryMonthlyDfDO item = new DwsSafeInventoryHistoryMonthlyDfDO();
                item.setStatTime(statTime);

                item.setProductType(e.getValue().get(0).getProductType());
                item.setCustomhouseTitle(e.getValue().get(0).getCustomhouseTitle());
                item.setAreaName(e.getValue().get(0).getAreaName());
                item.setRegionName(e.getValue().get(0).getRegionName());
                item.setZoneName(e.getValue().get(0).getZoneName());
                item.setInstanceType(e.getValue().get(0).getInstanceType());
                item.setCustomerCustomGroup(e.getValue().get(0).getCustomerCustomGroup());
                //  交付SLA
                BigDecimal sla = BigDecimal.valueOf(operationViewService.getSLAByInstanceType(item.getInstanceType(),
                        Objects.equals(item.getCustomhouseTitle(), "境内")));
                item.setSla(sla);

                // 13周交付周期
                String key = StringTools.join("@", item.getProductType(),
                        item.getCustomhouseTitle(), item.getAreaName(), item.getRegionName(), item.getZoneName(),
                        item.getInstanceType());
                List<BigDecimal> deliveryValues = operationViewService2Impl.calcDeliveryData(deliveryDfMap, key);
                if (deliveryValues != null) {
                    item.setDeliveryAvg(deliveryValues.get(0));
                    item.setDeliveryStandardDiff(deliveryValues.get(1));
                } else {
                    item.setDeliveryAvg(sla);
                    item.setDeliveryStandardDiff(null);
                }

                item.setAlgorithm(algorithm);
                List<BigDecimal> demands = ListUtils.transform(e.getValue(), o -> {
                    if (algorithm.equals("历史周峰")) {
                        return o.getWeekPeakLogicNum();
                    } else {
                        return o.getWeekDiffLogicNum();
                    }
                });
                BigDecimal demandSD = OperationViewTools.calculateSD(demands);
                item.setStandardDiff(demandSD);

                //  服务水平
                BigDecimal serviceLevel = operationViewService2Impl.getTargetServiceLevel(statTime, item.getZoneName(), item.getInstanceType(), "包年包月");
                item.setServiceLevel(serviceLevel);
                //  服务系数
                double serviceNum = OperationViewTools.normsinv(serviceLevel.doubleValue());
                item.setServiceLevelFactor(BigDecimal.valueOf(serviceNum));

                // 周均需求
                BigDecimal avgDemandNum = NumberUtils.avg(demands, 6);
                item.setDemandAvg(avgDemandNum);

                //  包月安全库存
                BigDecimal finalSla = item.getDeliveryAvg() != null ? item.getDeliveryAvg() : sla;
                BigDecimal monthlySafeInvNum = operationViewService2Impl.calMonthlyInvNum(serviceNum, finalSla, demandSD, avgDemandNum, item.getDeliveryStandardDiff());
                item.setMonthlySafetyInv(monthlySafeInvNum);
                BigDecimal noDelvieryMonthlySafeInvNum = operationViewService2Impl.calMonthlyInvNum(serviceNum, finalSla, demandSD, avgDemandNum, null);
                item.setNoDeliveryMonthlySafetyInv(noDelvieryMonthlySafeInvNum);
                result.add(item);
            }
        }

        return result;
    }

    private List<DwsSafeInventoryHistoryMonthlyDfDO> buildMckMonthlySafeInventoryData(String statTime) {
        OperationViewService2Impl operationViewService = SpringUtil.getBean(OperationViewService2Impl.class);

        List<DwsSafeInventoryHistoryMonthlyDfDO> result = ListUtils.newList();
        // 遍历所有可能的 customer custom group
        for (String customerCustomGroup : new String[] { "ALL", "MEDIUM_LONG_TAIL", "LIST_REPORT" }) {
            OperationViewReq2 req = new OperationViewReq2();
            req.setDate(DateUtils.parse(statTime));
            req.setCustomerCustomGroup(customerCustomGroup);

            OperationViewResp2 resp = new OperationViewResp2();
            // 调用 MCK 安全库存算法
            operationViewService.buildHistoryWeekPeakDemand(req, resp);

            result.addAll(resp.getData().stream().map(item -> {
                DwsSafeInventoryHistoryMonthlyDfDO monthlyDfDO = new DwsSafeInventoryHistoryMonthlyDfDO();
                monthlyDfDO.setStatTime(statTime);
                monthlyDfDO.setProductType(item.getProductType());
                monthlyDfDO.setAlgorithm("MCK历史&预测需求");
                monthlyDfDO.setCustomhouseTitle(item.getCustomhouseTitle());
                monthlyDfDO.setAreaName(item.getAreaName());
                monthlyDfDO.setRegionName(item.getRegionName());
                monthlyDfDO.setZoneName(item.getZoneName());
                monthlyDfDO.setInstanceType(item.getInstanceType());
                monthlyDfDO.setCustomerCustomGroup(customerCustomGroup);
                monthlyDfDO.setMonthlySafetyInv(item.getHistoryWeekPeakForecastWN().getMonthlySafetyInv());

                // 算不考虑供应影响的安全库存
                OperationViewResp2.SafetyInventoryResult historyWeekPeak = item.getHistoryWeekPeakForecastWN();
                BigDecimal finalSla = historyWeekPeak.getDeliveryAvg() != null ? historyWeekPeak.getDeliveryAvg() : BigDecimal.valueOf(historyWeekPeak.getSla());
                BigDecimal serviceNum = historyWeekPeak.getServiceLevelFactor();
                BigDecimal avgDemandNum = historyWeekPeak.getDemandAvg();
                BigDecimal demandSD = historyWeekPeak.getStandardDiff();
                BigDecimal noDeliveryMonthlyInvNum = operationViewService.calMonthlyInvNum(serviceNum.doubleValue(), finalSla, demandSD, avgDemandNum, null);
                monthlyDfDO.setNoDeliveryMonthlySafetyInv(noDeliveryMonthlyInvNum);

                monthlyDfDO.setStandardDiff(demandSD);
                monthlyDfDO.setDemandAvg(avgDemandNum);
                monthlyDfDO.setDeliveryAvg(historyWeekPeak.getDeliveryAvg());
                monthlyDfDO.setDeliveryStandardDiff(historyWeekPeak.getDeliveryStandardDiff());
                monthlyDfDO.setSla(BigDecimal.valueOf(historyWeekPeak.getSla()));
                monthlyDfDO.setServiceLevel(historyWeekPeak.getServiceLevel());
                monthlyDfDO.setServiceLevelFactor(historyWeekPeak.getServiceLevelFactor());

                return monthlyDfDO;
            }).collect(Collectors.toList()));
        }

        return result;
    }

    @Override
    @TaskLog(taskName = "genMonthlySafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genMonthlySafeInventoryData(String statTime) {
        // 清空当天的数据，或者 statTime 为空值的数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_safe_inventory_history_monthly_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
        List<DwsSafeInventoryHistoryMonthlyDfDO> historyMonthlySafeInventoryData = buildHistoryMonthlySafeInventoryData(statTime);

        // 生成 MCK 历史&预测需求算法数据
        List<DwsSafeInventoryHistoryMonthlyDfDO> mckMonthlySafeInventoryData = buildMckMonthlySafeInventoryData(statTime);
        historyMonthlySafeInventoryData.addAll(mckMonthlySafeInventoryData);
        ckcldDBHelper.insertBatchWithoutReturnId(historyMonthlySafeInventoryData);
    }

    @Override
    @TaskLog(taskName = "genMckMonthlySafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genMckMonthlySafeInventoryData(String statTime) {
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_safe_inventory_history_monthly_df_local ON CLUSTER default_cluster DELETE " +
                "where stat_time = ? and algorithm = 'MCK历史&预测需求'", statTime);

        // 生成 MCK 历史&预测需求算法数据
        List<DwsSafeInventoryHistoryMonthlyDfDO> mckMonthlySafeInventoryData = buildMckMonthlySafeInventoryData(statTime);
        ckcldDBHelper.insertBatchWithoutReturnId(mckMonthlySafeInventoryData);
    }

    @Override
    @TaskLog(taskName = "genActualInventoryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genActualInventoryData(String statTime) {
        // 清空当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_actual_inventory_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);

        String sql = "select 'CVM' as product_type, zone_name, customhouse_title, area_name, city_name as region_name, device_type, stock_class1, stock_class2, stock_class4, data_date as stat_time, sum(available_core_num) as actual_inv, sum(num) as device_num\n" +
                "from cubes.cloud_cvm_stock_data\n" +
                "where stat_time = ? and category='库存'\n" +
                "group by zone_name, customhouse_title, area_name, region_name, device_type, stock_class1, stock_class2, stock_class4, stat_time";

        List<GenActualInventoryDfDO> actualInventoryData = ckcubesDBHelper.getRaw(GenActualInventoryDfDO.class, sql, statTime);

//        Map<String, StaticStockPrincipalHosttypeDO> allGoodDeviceType = baseDictService.getAllGoodDeviceType();
        List<DwsActualInventoryDfDO> result = ListUtils.newList();
        for (GenActualInventoryDfDO actualInventoryDO : actualInventoryData) {
            DwsActualInventoryDfDO item = new DwsActualInventoryDfDO();
            item.setStatTime(statTime);
            item.setProductType(actualInventoryDO.getProductType());
            item.setCustomhouseTitle(actualInventoryDO.getCustomhouseTitle());
            item.setAreaName(actualInventoryDO.getAreaName());
            item.setRegionName(actualInventoryDO.getRegionName());
            item.setZoneName(actualInventoryDO.getZoneName());
            item.setDeviceType(actualInventoryDO.getDeviceType());
            // 从实例类型映射到设备类型
            String deviceFamily = dictService.getCsigInstanceTypeByDeviceType(actualInventoryDO.getDeviceType());
            if (StringTools.isBlank(deviceFamily)) {
                deviceFamily = dictService.getDeviceFamilyByDeviceType(actualInventoryDO.getDeviceType());
            }
            item.setInstanceType(StringTools.isBlank(deviceFamily) ? "" : deviceFamily);

            item.setMaterialType(actualInventoryDO.getMaterialType());
            item.setLineType(actualInventoryDO.getLineType());
            item.setInvDetailType(actualInventoryDO.getInvDetailType());
            item.setActualInv(actualInventoryDO.getActualInv());
            item.setDeviceNum(actualInventoryDO.getDeviceNum());
//            if (item.getLineType().equals("线下库存")) {
//                item.setMaterialType(allGoodDeviceType.containsKey(item.getDeviceType()) ? "好料" : "差料");
//                // 线下库存的库存细类只有一种，就是"大核库存"
//                item.setInvDetailType("大核库存");
//            }

            result.add(item);
        }

        ckcldDBHelper.insertBatchWithoutReturnId(result);
    }

    @Override
    @TaskLog(taskName = "genBufferSafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genBufferSafeInventoryData(String statTime) {
        // 清空当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_buffer_safe_inventory_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);

        List<DwsBufferSafeInventoryDfDO> result = ListUtils.newList();

        List<BufferAverageCoreDTO> bufferAverageCoreDTOS =
                outsideViewService.queryBufferCoreAverage(DateUtils.parse(statTime));
        QueryBufferPoolConfigReq req = new QueryBufferPoolConfigReq();
        req.setDate(statTime);
        List<InventoryHealthBufferConfigDO> bufferConfigDOS = (List<InventoryHealthBufferConfigDO>)SpringUtil.getBean(
                InventoryHealthConfigServiceImpl.class).queryBufferPoolConfig(req);
        Map<String, InventoryHealthBufferConfigDO> bufferConfigDOMap = ListUtils.toMap(bufferConfigDOS, o -> StringTools.join("@", o.getProduct(), o.getRegionType(), o.getInstanceType()), o -> o);


        for (BufferAverageCoreDTO bufferAverageCoreDTO : bufferAverageCoreDTOS) {
            DwsBufferSafeInventoryDfDO bufferInventoryDfDO = new DwsBufferSafeInventoryDfDO();
            bufferInventoryDfDO.setStatTime(statTime);
            bufferInventoryDfDO.setProductType("CVM");
            bufferInventoryDfDO.setCustomhouseTitle(bufferAverageCoreDTO.getCustomhouseTitle());
            bufferInventoryDfDO.setAreaName(bufferAverageCoreDTO.getAreaName());
            bufferInventoryDfDO.setRegionName(bufferAverageCoreDTO.getRegionName());
            bufferInventoryDfDO.setZoneName(bufferAverageCoreDTO.getZoneName());
            bufferInventoryDfDO.setInstanceType(bufferAverageCoreDTO.getInstanceType());

            BigDecimal bufferServiceLevel = SpringUtil.getBean(OperationViewService2Impl.class).getTargetServiceLevel(statTime, bufferInventoryDfDO.getZoneName(), bufferInventoryDfDO.getInstanceType(), "弹性");
            //  弹性服务水平/系数
            bufferInventoryDfDO.setBufferServiceLevel(bufferServiceLevel);
            bufferInventoryDfDO.setBufferServiceLevelFactor(
                    BigDecimal.valueOf(OperationViewTools.normsinv(bufferServiceLevel.doubleValue())));
            bufferInventoryDfDO.setBufferAverageStartDate(DateUtils.formatDate(bufferAverageCoreDTO.getAverageStartDate()));
            bufferInventoryDfDO.setBufferAverageEndDate(DateUtils.formatDate(bufferAverageCoreDTO.getAverageEndDate()));
            // 获取 ROI，如果 ROI 不存在，则默认为 1
            InventoryHealthBufferConfigDO bufferConfigDO = bufferConfigDOMap.get(StringTools.join("@", bufferInventoryDfDO.getProductType(), bufferInventoryDfDO.getCustomhouseTitle(), bufferInventoryDfDO.getInstanceType()));
            if (bufferConfigDO != null) {
                bufferInventoryDfDO.setMckBufferRoi(bufferConfigDO.getRoi());
                bufferInventoryDfDO.setMckBufferRate(bufferConfigDO.getRate());
            } else {
                bufferInventoryDfDO.setMckBufferRoi(BigDecimal.ONE);
                bufferInventoryDfDO.setMckBufferRate(BigDecimal.ONE);
            }
            //  弹性规模日均值
            bufferInventoryDfDO.setBufferAverageCore(BigDecimal.valueOf(bufferAverageCoreDTO.getBufferAverageCore()));
            BigDecimal mckBufferSafeInv = bufferInventoryDfDO.getBufferAverageCore().divide(bufferInventoryDfDO.getMckBufferRate(), 6, RoundingMode.HALF_UP);
            bufferInventoryDfDO.setMckBufferSafetyInv(NumberUtils.max(mckBufferSafeInv, BigDecimal.ZERO));
            //  弹性备货配额-三种算法目前结果完全相同
            bufferInventoryDfDO.setBufferSafetyInv(
                    AmountUtils.multiply(BigDecimal.valueOf(bufferAverageCoreDTO.getBufferAverageCore()), bufferInventoryDfDO.getBufferServiceLevel()));

            result.add(bufferInventoryDfDO);
        }

        ckcldDBHelper.insertBatchWithoutReturnId(result);
    }

    @Override
    @TaskLog(taskName = "genWeekNForecastData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genWeekNForecastData(String statTime) {
        // 清空当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_demand_week_n_ppl_version_item_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
//        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_demand_week_n_ppl_version_item_local ON CLUSTER default_cluster delete where stat_time = '1970-01-01'");

        // 这里的 map 先写死，后面迭代中支持配置按照 "机型 + 境内外" 配置
        List<DemandWeekNConfigDO> demandWeekNConfigDOS = ListUtils.newList(new DemandWeekNConfigDO("默认机型", "境内", 5),
                new DemandWeekNConfigDO("默认机型", "境外", 8));

        LocalDate statLocalDate = LocalDate.parse(statTime);

        // 取出配置的所有机型，用于"默认机型"的反选
        Set<String> demandWeekNConfigInstanceTypes = demandWeekNConfigDOS.stream().map(DemandWeekNConfigDO::getInstanceType).filter(ins -> !ins.equals("默认机型")).collect(Collectors.toSet());
        // 取出所有 N 相同的配置项
        Map<Integer, List<DemandWeekNConfigDO>> demandWeekNConfigMap = demandWeekNConfigDOS.stream().collect(Collectors.groupingBy(DemandWeekNConfigDO::getWeekN));

        // 查询所有园区类型分类，用于后续随机可用区的均摊逻辑
        QueryZoneConfigReq req = new QueryZoneConfigReq();
        List<InventoryHealthMainZoneNameConfigDO> allMainZoneNameConfigDOS = (List<InventoryHealthMainZoneNameConfigDO>)SpringUtil.getBean(InventoryHealthConfigServiceImpl.class).queryZoneConfig(req);
        // 按照城市分组
        Map<String, List<InventoryHealthMainZoneNameConfigDO>> allMainZoneNameConfigMap = allMainZoneNameConfigDOS.stream().collect(Collectors.groupingBy(InventoryHealthMainZoneNameConfigDO::getRegionName));

        List<DwsDemandWeekNPplVersionItemDO> result = ListUtils.newList();

        InventoryHealthDictService inventoryHealthDictService = SpringUtil.getBean(InventoryHealthDictServiceImpl.class);
        // 1. 找到过去 13 周
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = inventoryHealthDictService.getHolidayWeekInfoBase(statLocalDate, -13);
        // 2. 找到过去 13 周的 WN 预测
        for (HolidayWeekInfoDTO holidayWeekInfoDTO : holidayWeekInfoDTOS) {
            // 对于每一周，按照不同的机型配置
            for (Map.Entry<Integer, List<DemandWeekNConfigDO>> entry : demandWeekNConfigMap.entrySet()) {
                int n = entry.getKey();
                Set<String> instanceTypes = entry.getValue().stream().map(DemandWeekNConfigDO::getInstanceType).collect(Collectors.toSet());
                Set<String> customhouseTitles = entry.getValue().stream().map(DemandWeekNConfigDO::getCustomhouseTitle).collect(Collectors.toSet());
                // 拿到过去 N 周的节假周信息
                List<HolidayWeekInfoDTO> lastNWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(LocalDate.parse(holidayWeekInfoDTO.getStartDate()), -n);
                // 找到最小的开始时间
                HolidayWeekInfoDTO weekInfoDTO = lastNWeekInfo.get(0);

                WhereSQL condition = new WhereSQL();

                // 加上机型的筛选，如果包含默认机型，取配置的机型以及反选的机型的并集
                if (instanceTypes.contains("默认机型")) {
                    // 排除默认机型
                    List<String> selectedInstanceTypes = instanceTypes.stream().filter(ins -> !ins.equals("默认机型")).collect(Collectors.toList());
                    // 只有存在非默认机型的时候
                    if (ListUtils.isNotEmpty(demandWeekNConfigInstanceTypes) && !ListUtils.isNotEmpty(selectedInstanceTypes)) {
                        condition.and("(instance_type in (?) or instance_type not in (?))", selectedInstanceTypes, demandWeekNConfigInstanceTypes);
                    }
                } else {
                    condition.and("instance_type in (?)", instanceTypes);
                }
                // 境内外的筛选
                condition.and("customhouse_title in (?)", customhouseTitles);

                // 剔除弹性需求
                condition.and("demand_type <> 'ELASTIC'");

                condition.and("version_code_date between ? and ?", weekInfoDTO.getStartDate(), weekInfoDTO.getEndDate());
                condition.and("version_group_product = 'CVM&CBS'");
                // 云运管干预后的所有 PPL
                condition.and("source in ('IMPORT', 'COMD_INTERVENE', 'FORECAST', 'APPLY_AUTO_FILL', 'SYNC_YUNXIAO')");
                condition.and("is_comd = 0");

                // 购买日期区间需要和当周的时间有交集
                WhereSQL buyDateCondition = new WhereSQL();
                buyDateCondition.or("begin_buy_date between ? and ?", holidayWeekInfoDTO.getStartDate(), holidayWeekInfoDTO.getEndDate());
                buyDateCondition.or("end_buy_date between ? and ?", holidayWeekInfoDTO.getStartDate(), holidayWeekInfoDTO.getEndDate());
                buyDateCondition.or("begin_buy_date < ? and end_buy_date > ?", holidayWeekInfoDTO.getStartDate(), holidayWeekInfoDTO.getEndDate());
                condition.and(buyDateCondition);

//                WhereSQL categoryCondition = genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getCategoryDate());
//                condition.and(categoryCondition);

                condition.addGroupBy("version_code_date", "begin_buy_date", "end_buy_date", "instance_type", "customhouse_title", "area_name", "region_name", "zone_name", "source", "year", "month");

                List<PplVersionItemDO> pplVersionItemDOS = ckcldStdCrpDBHelper.getAll(PplVersionItemDO.class, condition.getSQL(), condition.getParams());

                // 3. 处理跨周的场景
                //  3.1 对于大客户提报需求，将预测平均到所跨的周。从预测的开始结束购买时间得到所跨的周数
                //  3.2 对于中长尾需求，中长尾是按月预测，所以这里要平均到 4 或者 5 周，基于当月有多少个节假周
                result.addAll(handlePplData(statTime,  holidayWeekInfoDTO, pplVersionItemDOS, n, allMainZoneNameConfigMap, false));
            }
        }

        ckcldDBHelper.insertBatchWithoutReturnId(result);
    }

    @Override
    @TaskLog(taskName = "genMckTurnoverInventoryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genMckTurnoverInventoryData(String statTime) {
        ResPlanHolidayWeekDO lastWeek = getLastWeek(statTime);
        String yearWeek = lastWeek.getYear().toString() + lastWeek.getWeek().toString();
        // 清空当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_inventory_health_mck_turnover_wf_local ON CLUSTER default_cluster DROP PARTITION ?", yearWeek);

        // 每周生成一份前一周的周转库存数据，周转库存算法：max(周实际净消耗峰值, 0) + 本周预扣闲置量
        // 1. 先拿上周周净峰
        List<DwsInventoryHealthWeeklyScaleDfDO> all = getLastWeekPeakData(statTime);

        // 2. 再拿上周预扣闲置量
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time between ? and ?", lastWeek.getStart(), lastWeek.getEnd());
        condition.and("product_type = 'CVM'");
        // 只要预扣数据
        condition.and("inv_detail_type = '用户预扣'");
        List<DwsActualInventoryDfDO> reservedItems = ckcldDBHelper.getAll(DwsActualInventoryDfDO.class, condition.getSQL(), condition.getParams());

        // 3. 合并周峰和预扣数据
        List<DwsInventoryHealthMckTurnoverWfDO> turnoverDOS = ListUtils.merge(
                all,
                reservedItems,
                o -> StringTools.join("@", o.getProductType(), o.getInstanceType(), o.getZoneName()),
                o -> StringTools.join("@", o.getProductType(), o.getInstanceType(), o.getZoneName()),
                (o1, o2) -> {
                    DwsInventoryHealthMckTurnoverWfDO turnoverDO = new DwsInventoryHealthMckTurnoverWfDO();

                    if (ListUtils.isNotEmpty(o1)) {
                        DwsInventoryHealthWeeklyScaleDfDO scaleDfDO = o1.get(0);
                        turnoverDO.setProductType(scaleDfDO.getProductType());
                        turnoverDO.setYearWeek(yearWeek);
                        turnoverDO.setHolidayYear(scaleDfDO.getHolidayYear());
                        turnoverDO.setHolidayMonth(scaleDfDO.getHolidayMonth());
                        turnoverDO.setHolidayWeek(scaleDfDO.getHolidayWeek());
                        turnoverDO.setCustomhouseTitle(scaleDfDO.getCustomhouseTitle());
                        turnoverDO.setAreaName(scaleDfDO.getAreaName());
                        turnoverDO.setRegionName(scaleDfDO.getRegionName());
                        turnoverDO.setZoneName(scaleDfDO.getZoneName());
                        turnoverDO.setInstanceType(scaleDfDO.getInstanceType());
                        turnoverDO.setHolidayWeekStartDate(scaleDfDO.getHolidayWeekStartDate());
                        turnoverDO.setHolidayWeekEndDate(scaleDfDO.getHolidayWeekEndDate());
                    } else {
                        DwsActualInventoryDfDO reservedItem = o2.get(0);
                        turnoverDO.setProductType(reservedItem.getProductType());
                        turnoverDO.setYearWeek(yearWeek);
                        turnoverDO.setHolidayYear(lastWeek.getYear());
                        turnoverDO.setHolidayMonth(lastWeek.getMonth());
                        turnoverDO.setHolidayWeek(lastWeek.getWeek());
                        turnoverDO.setCustomhouseTitle(reservedItem.getCustomhouseTitle());
                        turnoverDO.setAreaName(reservedItem.getAreaName());
                        turnoverDO.setRegionName(reservedItem.getRegionName());
                        turnoverDO.setZoneName(reservedItem.getZoneName());
                        turnoverDO.setInstanceType(reservedItem.getInstanceType());
                        turnoverDO.setHolidayWeekStartDate(LocalDate.parse(lastWeek.getStart()));
                        turnoverDO.setHolidayWeekEndDate(LocalDate.parse(lastWeek.getEnd()));
                    }

                    BigDecimal weekPeakLogicNum = BigDecimal.ZERO;
                    if (ListUtils.isNotEmpty(o1)) {
                        weekPeakLogicNum  = o1.get(0).getWeekPeakLogicNum();
                    }
                    turnoverDO.setWeekPeakCore(weekPeakLogicNum);

                    BigDecimal weekReservedAvgCore = BigDecimal.ZERO;
                    if (ListUtils.isNotEmpty(o2)) {
                        weekReservedAvgCore = NumberUtils.avg(o2, 2, o -> o.getActualInv());
                    }

                    turnoverDO.setWeekReservedAvgCore(weekReservedAvgCore);
                    turnoverDO.setTurnoverInv(turnoverDO.getWeekPeakCore().add(turnoverDO.getWeekReservedAvgCore()));
                    // 赋值属性
                    return turnoverDO;
                }
        );

        ckcldDBHelper.insertBatchWithoutReturnId(turnoverDOS);
    }

    @Override
    @TaskLog(taskName = "genMckForecastTurnoverInventoryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genMckForecastTurnoverInventoryData(String statTime) {
        // 清空当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_inventory_health_mck_forecast_turnover_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
        // 预测周转库存=（T0周过去12周周峰均值*12+最新版本的本周预测净增值）/13；

        // 1. 取过去 12 周周峰均值
        List<DwsInventoryHealthWeeklyScaleDfDO> all = getLastWeekPeakData(statTime);

        // 2. 取最新版本的预测，先取最新版本，再取改版本的预测
        String sql = "select version_code\n" +
                "from std_crp.dwd_crp_ppl_item_version_cf\n" +
                "where version_status = 'DONE'\n" +
                "and version_code_date <= ?\n" +
                "and version_code like 'V_%'\n" +
                "group by version_code\n" +
                "order by version_code desc\n" +
                "limit 1";
        String versionCode = ckcldStdCrpDBHelper.getRaw(String.class, sql, statTime).get(0);

        WhereSQL condition = new WhereSQL();
        condition.and("version_code = ?", versionCode);
        // 剔除弹性需求
        condition.and("demand_type <> 'ELASTIC'");
        // 暂时只要 CVM
        condition.and("version_group_product = 'CVM&CBS'");
        // 云运管干预后的所有 PPL
        condition.and("source in ('IMPORT', 'COMD_INTERVENE', 'FORECAST', 'APPLY_AUTO_FILL', 'SYNC_YUNXIAO')");
        condition.and("is_comd = 0");
        condition.addGroupBy("version_code_date", "begin_buy_date", "end_buy_date", "instance_type", "customhouse_title", "area_name", "region_name", "zone_name", "source", "year", "month");

        List<PplVersionItemDO> pplVersionItemDOS = ckcldStdCrpDBHelper.getAll(PplVersionItemDO.class, condition.getSQL(), condition.getParams());

        // 3. 计算当周 + 未来 13 周的预测周转库存
        LocalDate statLocalDate = LocalDate.parse(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = ListUtils.newList();
        // 当周也算
        ResPlanHolidayWeekDO curWeek = baseDictService.getHolidayWeekInfoByDate(statTime);
        HolidayWeekInfoDTO curWeekDTO = new HolidayWeekInfoDTO();
        curWeekDTO.setYear(curWeek.getYear());
        curWeekDTO.setMonth(curWeek.getMonth());
        curWeekDTO.setWeek(curWeek.getWeek());
        curWeekDTO.setStartDate(curWeek.getStart());
        curWeekDTO.setEndDate(curWeek.getEnd());
        curWeekDTO.setWeekNFromNow(0);
        holidayWeekInfo.add(curWeekDTO);
        // 未来13周
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = inventoryHealthDictService.getHolidayWeekInfoBase(statLocalDate, 13);
        holidayWeekInfo.addAll(holidayWeekInfoDTOS);

        // 查询所有园区类型分类，用于后续随机可用区的均摊逻辑
        QueryZoneConfigReq req = new QueryZoneConfigReq();
        List<InventoryHealthMainZoneNameConfigDO> allMainZoneNameConfigDOS = (List<InventoryHealthMainZoneNameConfigDO>)SpringUtil.getBean(InventoryHealthConfigServiceImpl.class).queryZoneConfig(req);
        // 按照城市分组
        Map<String, List<InventoryHealthMainZoneNameConfigDO>> allMainZoneNameConfigMap = allMainZoneNameConfigDOS.stream().collect(Collectors.groupingBy(InventoryHealthMainZoneNameConfigDO::getRegionName));

        List<DwsInventoryHealthMckForecastTurnoverDfDO> result = ListUtils.newList();

        for (HolidayWeekInfoDTO weekInfoDTO : holidayWeekInfo) {
            // 找到所有落在该周时间范围内的预测，跨周的需求进行均摊，跟WN预测生成的逻辑一致
            List<DwsDemandWeekNPplVersionItemDO> demandWeekNPplVersionItemDOS = handlePplData(statTime, weekInfoDTO, pplVersionItemDOS, 0, allMainZoneNameConfigMap, true);

            result.addAll(ListUtils.merge(
                    all,
                    demandWeekNPplVersionItemDOS,
                    o -> StringTools.join("@", o.getProductType(), o.getInstanceType(), o.getZoneName()),
                    o -> StringTools.join("@", o.getProductType(), o.getInstanceType(), o.getZoneName()),
                    (o1, o2) -> {
                        DwsInventoryHealthMckForecastTurnoverDfDO turnoverDfDO = new DwsInventoryHealthMckForecastTurnoverDfDO();
                        turnoverDfDO.setStatTime(LocalDate.parse(statTime));
                        turnoverDfDO.setHolidayYear(weekInfoDTO.getYear());
                        turnoverDfDO.setHolidayMonth(weekInfoDTO.getMonth());
                        turnoverDfDO.setHolidayWeek(weekInfoDTO.getWeek());
                        turnoverDfDO.setHolidayWeekStartDate(LocalDate.parse(weekInfoDTO.getStartDate()));
                        turnoverDfDO.setHolidayWeekEndDate(LocalDate.parse(weekInfoDTO.getEndDate()));
                        turnoverDfDO.setWeekIndex(weekInfoDTO.getWeekNFromNow());

                        if (ListUtils.isNotEmpty(o1)) {
                            DwsInventoryHealthWeeklyScaleDfDO scaleDfDO = o1.get(0);
                            turnoverDfDO.setProductType(scaleDfDO.getProductType());
                            turnoverDfDO.setInstanceType(scaleDfDO.getInstanceType());
                            turnoverDfDO.setCustomhouseTitle(scaleDfDO.getCustomhouseTitle());
                            turnoverDfDO.setAreaName(scaleDfDO.getAreaName());
                            turnoverDfDO.setRegionName(scaleDfDO.getRegionName());
                            turnoverDfDO.setZoneName(scaleDfDO.getZoneName());
                        } else {
                            DwsDemandWeekNPplVersionItemDO versionItemDO = o2.get(0);
                            turnoverDfDO.setProductType(versionItemDO.getProductType());
                            turnoverDfDO.setInstanceType(versionItemDO.getInstanceType());
                            turnoverDfDO.setCustomhouseTitle(versionItemDO.getCustomhouseTitle());
                            turnoverDfDO.setAreaName(versionItemDO.getAreaName());
                            turnoverDfDO.setRegionName(versionItemDO.getRegionName());
                            turnoverDfDO.setZoneName(versionItemDO.getZoneName());
                        }

                        if (ListUtils.isNotEmpty(o1)) {
                            turnoverDfDO.setWeekPeakCore(NumberUtils.sum(o1, o -> o.getWeekPeakLogicNumAvg12()));
                        } else {
                            turnoverDfDO.setWeekPeakCore(BigDecimal.ZERO);
                        }

                        if (ListUtils.isNotEmpty(o2)) {
                            turnoverDfDO.setAvgForecastCore(NumberUtils.sum(o2, o -> o.getAverageTotalCore()));
                            turnoverDfDO.setTotalForecastCore(NumberUtils.sum(o2, o -> o.getTotalCore()));
                        } else {
                            turnoverDfDO.setAvgForecastCore(BigDecimal.ZERO);
                            turnoverDfDO.setTotalForecastCore(BigDecimal.ZERO);
                        }

                        BigDecimal totalWeekPeakCore = turnoverDfDO.getWeekPeakCore().multiply(BigDecimal.valueOf(12));
                        BigDecimal totalCore = totalWeekPeakCore.add(turnoverDfDO.getAvgForecastCore());
                        turnoverDfDO.setTurnoverInv(totalCore.divide(BigDecimal.valueOf(13), 6, RoundingMode.HALF_UP));

                        return turnoverDfDO;
                    }
            ));
        }

        ckcldDBHelper.insertBatchWithoutReturnId(result);
    }

    @Override
    @TaskLog(taskName = "genMckForecastSafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genMckForecastSafeInventoryData(String statTime) {
        // 清空当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_inventory_health_mck_forecast_monthly_safe_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);

        // 预测包月安全库存：
        //  基于历史13周的安全库存与实际周峰净增量，计算出安全库存的周转周数M=13周安全库存均值/13周实际周峰净增均值
        //  预测安全库存预估=周转周数M*预测周转库存

        // 1. 取 13 周包月安全库存均值
        String safeInvSql = "select product_type, customhouse_title, area_name, region_name, zone_name, instance_type, avg(monthly_safety_inv) cores1, avg(no_delivery_monthly_safety_inv) cores2\n" +
                "from cloud_demand.dws_safe_inventory_history_monthly_df\n" +
                "where stat_time between ? and ?\n" +
                "  and algorithm = 'MCK历史&预测需求'\n" +
                "  and customer_custom_group = 'ALL'\n" +
                "group by product_type, customhouse_title, area_name, region_name, zone_name, instance_type";
        // 获取过去 13 周
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = inventoryHealthDictService.getHolidayWeekInfoBase(LocalDate.parse(statTime), -13);
        String start = holidayWeekInfoDTOS.get(0).getStartDate();
        String end = holidayWeekInfoDTOS.get(holidayWeekInfoDTOS.size() - 1).getEndDate();
        List<GenMckForecastMonthlySafeInvDfDO> monthlySafeInvDfDOS = ckcldDBHelper.getRaw(GenMckForecastMonthlySafeInvDfDO.class, safeInvSql, start, end);
        // 2. 取 13 周实际周峰净增均值
        String weekPeakSql = "select product_type, customhouse_title, area_name, region_name, zone_name, instance_type, avg(week_peak_logic_num_avg_13) cores1, 0 cores2\n" +
                "from cloud_demand.dws_inventory_health_weekly_scale_df\n" +
                "where stat_time = ?\n" +
                "  and week_index = -1\n" +
                "  and customer_custom_group = 'ALL'\n" +
                "  and exclude_uin_list = '(空值)'\n" +
                "group by product_type, customhouse_title, area_name, region_name, zone_name, instance_type";
        List<GenMckForecastMonthlySafeInvDfDO> weekPeakDfDOS = ckcldDBHelper.getRaw(GenMckForecastMonthlySafeInvDfDO.class, weekPeakSql, statTime);

        Map<String, GenMckForecastMonthlySafeInvDfDO> monthlySafeInvDfDOMap = ListUtils.toMap(monthlySafeInvDfDOS, o -> o.toKey(), o -> o);
        Map<String, GenMckForecastMonthlySafeInvDfDO> weekPeakDfDOMap = ListUtils.toMap(weekPeakDfDOS, o -> o.toKey(), o -> o);

        // 3. 取未来 13 周的预测周转库存
        List<DwsInventoryHealthMckForecastTurnoverDfDO> forecastTurnoverDfDOS = ckcldDBHelper.getAll(DwsInventoryHealthMckForecastTurnoverDfDO.class, "where stat_time=?", statTime);

        // 4. 基于 1,2,3，计算未来 13 周的预测安全库存
        List<DwsInventoryHealthMckForecastMonthlySafeDfDO> forecastMonthlySafeDfDOS = forecastTurnoverDfDOS.stream().map(turnover -> {
            DwsInventoryHealthMckForecastMonthlySafeDfDO forecastMonthlySafeDfDO = DwsInventoryHealthMckForecastMonthlySafeDfDO.from(turnover);
            // 计算周转周数
            String key = DwsInventoryHealthMckForecastMonthlySafeDfDO.toGroupKey(forecastMonthlySafeDfDO);

            GenMckForecastMonthlySafeInvDfDO monthlySafeInvDfDO = monthlySafeInvDfDOMap.get(key);
            if (monthlySafeInvDfDO != null) {
                // core1 和 core2 分别表示包含供应影响的安全库存和不包含供应影响的安全库存
                forecastMonthlySafeDfDO.setSafeInvAvg13(monthlySafeInvDfDO.getCores1());
                forecastMonthlySafeDfDO.setNoDeliverySafeInvAvg13(monthlySafeInvDfDO.getCores2());
            } else {
                forecastMonthlySafeDfDO.setSafeInvAvg13(BigDecimal.ZERO);
                forecastMonthlySafeDfDO.setNoDeliverySafeInvAvg13(BigDecimal.ZERO);
            }

            GenMckForecastMonthlySafeInvDfDO weekPeakDfDO = weekPeakDfDOMap.get(key);
            if (weekPeakDfDO != null) {
                // 周净峰
                forecastMonthlySafeDfDO.setWeekPeakAvg13(weekPeakDfDO.getCores1());
            } else {
                forecastMonthlySafeDfDO.setWeekPeakAvg13(BigDecimal.ZERO);
            }

            // 周转周数 = 13周安全库存均值/13周实际周峰净增均值
            if (!forecastMonthlySafeDfDO.getWeekPeakAvg13().equals(BigDecimal.ZERO)) {
                forecastMonthlySafeDfDO.setTurnoverWeekNum(forecastMonthlySafeDfDO.getSafeInvAvg13().divide(forecastMonthlySafeDfDO.getWeekPeakAvg13(), 6, RoundingMode.HALF_UP));
                forecastMonthlySafeDfDO.setNoDeliveryTurnoverWeekNum(forecastMonthlySafeDfDO.getNoDeliverySafeInvAvg13().divide(forecastMonthlySafeDfDO.getWeekPeakAvg13(), 6, RoundingMode.HALF_UP));
            } else {
                forecastMonthlySafeDfDO.setTurnoverWeekNum(BigDecimal.ZERO);
                forecastMonthlySafeDfDO.setNoDeliveryTurnoverWeekNum(BigDecimal.ZERO);
            }

            forecastMonthlySafeDfDO.setTurnoverInv(turnover.getTurnoverInv());

            //  预测包月安全库存预估=周转周数M*预测周转库存
            BigDecimal monthlySafeInv = forecastMonthlySafeDfDO.getTurnoverWeekNum().multiply(forecastMonthlySafeDfDO.getTurnoverInv());
            forecastMonthlySafeDfDO.setMonthlySafeInv(monthlySafeInv);
            BigDecimal noDeliveryMonthlySafeInv = forecastMonthlySafeDfDO.getNoDeliveryTurnoverWeekNum().multiply(forecastMonthlySafeDfDO.getTurnoverInv());
            forecastMonthlySafeDfDO.setNoDeliveryMonthlySafeInv(noDeliveryMonthlySafeInv);

            return forecastMonthlySafeDfDO;
        }).collect(Collectors.toList());

        ckcldDBHelper.insertBatchWithoutReturnId(forecastMonthlySafeDfDOS);
    }

    @TaskLog(taskName = "genServiceLevelSoldData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Override
    public void genServiceLevelSoldData(String unix, String statTime) {
        //先清除当天的数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dwd_sold_out_data_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);
        //获取三个产品的售罄率相关数据
        List<DwsSoldOutDataDfDO> all = new ArrayList<>();
        //1.获取EMR的售罄率相关数据
        List<DwsSoldOutDataDfDO> emrSoldData = getEMRSoldData(unix, statTime);
        if (ListUtils.isNotEmpty(emrSoldData)) {
            all.addAll(emrSoldData);
        }
        //2.获取ES的售罄率相关数据
        //3.获取TC的售罄率相关数据
        ckcldDBHelper.insert(all);
    }

    public List<DwsSoldOutDataDfDO> getEMRSoldData(String unix, String statTime) {
        HttpHeaders header = new HttpHeaders();
        header.add("Accept", "application/json");
        header.add("Content-Type", "application/x-www-form-urlencoded");
        header.add("Authorization",
                "Bearer eyJrIjoiME9OQjBBUHlkRkhJMkNrY1RGMW56NjhSd1dmbk43c3EiLCJuIjoiZW1yIiwiaWQiOjF9");
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        String query = "sum(emrcc_spec_sellout_count{}) by (parentSpec, zone, sellout)";
        params.add("query", query);
        params.add("time", unix);
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(params, header);
        RestTemplate restTemplate = new RestTemplate();
        String temp = restTemplate.postForObject(emrUrl, entity, String.class);
        EMRJson emrJson = JSON.parseObject(temp, EMRJson.class);
        List<Item> data = new ArrayList<>();
        if (emrJson != null) {
            data = emrJson.getData().getResult();
        }
        Map<String, List<Item>> mapList = ListUtils.toMapList(data, o -> {
            String metric = o.getMetric();
            JSONObject jsonObject = JSON.parseObject(metric);
            return String.join("@", jsonObject.getString("zone"), jsonObject.getString("parentSpec"));
        }, o -> o);
        List<DwsSoldOutDataDfDO> all = new ArrayList<>();
        List<StaticZoneDO> zones = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, String> map = ListUtils.toMap(zones, o -> o.getZone(), o -> o.getZoneName());
        for (Entry<String, List<Item>> entry : mapList.entrySet()) {
            DwsSoldOutDataDfDO sold = new DwsSoldOutDataDfDO();
            String[] split = entry.getKey().split("@");
            sold.setZoneName(map.get(split[0]));
            sold.setInstanceFamily(split[1]);
            BigDecimal soldOut = BigDecimal.ZERO;
            BigDecimal total = BigDecimal.ZERO;
            List<Item> value = entry.getValue();
            if (ListUtils.isNotEmpty(value)) {
                soldOut = NumberUtils.sum(value.stream().filter(o -> {
                    String metric = o.getMetric();
                    JSONObject jsonObject = JSONObject.parseObject(metric);
                    return jsonObject.getInteger("sellout") == 1;
                }).collect(Collectors.toList()), o -> o.getValue().get(1));
                total = NumberUtils.sum(value, o -> o.getValue().get(1));
            }
            sold.setSoldOutNum(soldOut);
            sold.setTotalNum(total);
            sold.setStatTime(statTime);
            sold.setProductType("EMR");
            all.add(sold);
        }
        return all;
    }

    @Override
    @TaskLog(taskName = "genServiceLevelApiSuccessData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genServiceLevelApiSuccessData(String unix, String statTime) {
        //清除当天的数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dwd_api_success_data_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);
        //获取三个产品有关api成功率的数据
        List<DwsApiSuccessDataDfLocalDO> all = new ArrayList<>();
        //1.EMRAPI成功率数据
        List<DwsApiSuccessDataDfLocalDO> emrApiSuccessData = getEMRApiSuccessData(unix, statTime);
        if (ListUtils.isNotEmpty(emrApiSuccessData)) {
            all.addAll(emrApiSuccessData);
        }
        //2.ESAPI成功率数据
        //3.TCAPI成功率数据
        ckcldDBHelper.insert(all);
    }

    @Override
    public void genLeisureAndBusySoldOutData(String statTime) {
        //清除当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_leisure_and_busy_sold_out_data_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);
        String sql = "select\n"
                + "    date_format(a.stattime, '%Y%m%d') as imp_date,\n"
                + "    b.zone_name,\n"
                + "    b.zoneid as zone_id,\n"
                + "    ginsfamily as instance_type,\n"
                + "    sum(sold_cnt) as sold_total,\n"
                + "    sum(soldout_cnt) as sold_out_total,\n"
                + "    sum(if(hour(a.stattime) <=8, sold_cnt, 0))  as leisure_sold_total,\n"
                + "    sum(if(hour(a.stattime) <=8, soldout_cnt, 0)) as leisure_sold_out_total,\n"
                + "    sum(if(hour(a.stattime) >8, sold_cnt, 0))  as busy_sold_total,\n"
                + "    sum(if(hour(a.stattime) >8, soldout_cnt, 0)) as busy_sold_out_total\n"
                + "from hourly_soldout_rate a left join static_zone b on a.zoneid = b.zoneid\n"
                + "where date_format(a.stattime, '%Y%m%d') = ?\n"
                + "group by imp_date,b.zone_name,b.zoneid,ginsfamily;";
        List<DwsLeisureAndBusySoldOutDataDfDO> all = planDBHelper.getRaw(DwsLeisureAndBusySoldOutDataDfDO.class, sql,
                statTime);
        if (ListUtils.isNotEmpty(all)) {
            ckcldDBHelper.insert(all);
        }
    }

    @Override
    @TaskLog(taskName = "genInventoryHealthWeekData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genInventoryHealthWeekData(String statTime) {
        DictServiceImpl dictBean = SpringUtil.getBean(DictServiceImpl.class);
        ResPlanHolidayWeekDO aDo = dictBean.getHolidayWeekInfoByDate(statTime);
        String yearWeek = aDo.getYear() + "W" + aDo.getWeek();
        //清除当天数据
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_inventory_health_week_data_local ON CLUSTER default_cluster DROP PARTITION ?",
                yearWeek);
        InventoryHealthActualReq req = new InventoryHealthActualReq();
        req.setDate(statTime);
        req.setTimeDimension("周日均");
        req.setSoldType("全时段");
        List<CustomerCustomGroupEnum> cusList = Lang.list(CustomerCustomGroupEnum.ALL, CustomerCustomGroupEnum.MEDIUM_LONG_TAIL, CustomerCustomGroupEnum.LIST_REPORT);
        List<DwsInventoryHealthWeekDataDO> result = new ArrayList<>();
        InventoryHealthActualV2ServiceImpl bean = SpringUtil.getBean(InventoryHealthActualV2ServiceImpl.class);
        for (CustomerCustomGroupEnum custom : cusList) {
            for(int i = 0; i < 2; i++) {
                if (i == 0) {
                    req.setIsIncludeReserved(false);
                }else {
                    req.setIsIncludeReserved(true);
                }
                req.setCustomerCustomGroup(custom.getCode());
                InventoryHealthActualResp resp = bean.queryInventoryHealthActualByWeekAvg(req);
                for (InventoryHealthActualResp.Item datum : resp.getData()) {
                    DwsInventoryHealthWeekDataDO data = new DwsInventoryHealthWeekDataDO();
                    data.setYearWeek(yearWeek);
                    data.setInstanceType(datum.getInstanceType());
                    data.setZoneName(datum.getZoneName());
                    data.setCustomhouseTitle(datum.getCustomhouseTitle());
                    data.setAreaName(datum.getAreaName());
                    data.setRegionName(datum.getRegionName());
                    if (datum.getBufferAverageStartDate() != null) {
                        data.setBufferAvgStartDate(DateUtils.parseLocalDate(datum.getBufferAverageStartDate()));
                    }else {
                        data.setBufferAvgStartDate(DateUtils.parseLocalDate("1997-01-01"));
                    }
                    if (datum.getBufferAverageEndDate() != null) {
                        data.setBufferAvgEndDate(DateUtils.parseLocalDate(datum.getBufferAverageEndDate()));
                    }else {
                        data.setBufferAvgEndDate(DateUtils.parseLocalDate("1997-01-01"));
                    }
                    data.setCustomerCustomGroup(custom.getCode());
                    data.setIsIncludeReserved(!req.getIsIncludeReserved() ? 0 : 1);
                    data.setDeliveryAvg(datum.getDeliveryAvg());
                    data.setServiceLevel(datum.getServiceLevel());
                    data.setBufferServiceLevelFactor(datum.getBufferServiveLevelFactor());
                    data.setServiceLevelFactor(datum.getServiceLevelFactor());
                    data.setBufferServiceLevel(datum.getBufferServiceLevel());
                    data.setPrePaidSafetyInventoryCore(datum.getPrePaidSafetyInventoryCore());
                    data.setBufferAverageCore(datum.getBufferAverageCore());
                    data.setBufferSafetyInventoryCore(datum.getBufferSafetyInventoryCore());
                    data.setSafeInvManualConfig(datum.getSafeInvManualConfig());
                    data.setSafetyInventoryCore(datum.getSafetyInventoryCore());
                    data.setTurnoverWeekReservedAvgCore(datum.getTurnoverWeekReservedAvgCore());
                    data.setTurnoverInv(datum.getTurnoverInv());
                    data.setTurnoverWeekPeakCore(datum.getTurnoverWeekPeakCore());
                    result.add(data);
                }
            }
        }
        if (ListUtils.isNotEmpty(result)) {
            ckcldDBHelper.insertBatchWithoutReturnId(result);
        }
    }

    @Override
    @TaskLog(taskName = "genInventoryHealthMonthData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genInventoryHealthMonthData(String statTime) {
        //清除当天数据
        LocalDate localDate = DateUtils.parseLocalDate(statTime);
        String yearMonth = YearMonth.of(localDate.getYear(), localDate.getMonthValue()).toString();
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_inventory_health_month_data_local ON CLUSTER default_cluster DROP PARTITION ?",
                yearMonth);
        InventoryHealthActualReq req = new InventoryHealthActualReq();
        req.setDate(statTime);
        req.setTimeDimension("月日均");
        req.setSoldType("全时段");
        List<CustomerCustomGroupEnum> cusList = Lang.list(CustomerCustomGroupEnum.ALL, CustomerCustomGroupEnum.MEDIUM_LONG_TAIL, CustomerCustomGroupEnum.LIST_REPORT);
        List<DwsInventoryHealthMonthDataDO> result = new ArrayList<>();
        InventoryHealthActualV2ServiceImpl bean = SpringUtil.getBean(InventoryHealthActualV2ServiceImpl.class);
        for (CustomerCustomGroupEnum custom : cusList) {
            for(int i = 0; i < 2; i++) {
                if (i == 0) {
                    req.setIsIncludeReserved(false);
                }else {
                    req.setIsIncludeReserved(true);
                }
                req.setCustomerCustomGroup(custom.getCode());
                InventoryHealthActualResp resp = bean.queryInventoryHealthActualByMonthAvg(req);
                for (InventoryHealthActualResp.Item datum : resp.getData()) {
                    DwsInventoryHealthMonthDataDO data = new DwsInventoryHealthMonthDataDO();
                    data.setYearMonth(yearMonth);
                    data.setInstanceType(datum.getInstanceType());
                    data.setZoneName(datum.getZoneName());
                    data.setCustomhouseTitle(datum.getCustomhouseTitle());
                    data.setAreaName(datum.getAreaName());
                    data.setRegionName(datum.getRegionName());
                    if (datum.getBufferAverageStartDate() != null) {
                        data.setBufferAvgStartDate(DateUtils.parseLocalDate(datum.getBufferAverageStartDate()));
                    }else {
                        data.setBufferAvgStartDate(DateUtils.parseLocalDate("1997-01-01"));
                    }
                    if (datum.getBufferAverageEndDate() != null) {
                        data.setBufferAvgEndDate(DateUtils.parseLocalDate(datum.getBufferAverageEndDate()));
                    }else {
                        data.setBufferAvgEndDate(DateUtils.parseLocalDate("1997-01-01"));
                    }
                    data.setCustomerCustomGroup(custom.getCode());
                    data.setIsIncludeReserved(!req.getIsIncludeReserved() ? 0 : 1);
                    data.setDeliveryAvg(datum.getDeliveryAvg());
                    data.setServiceLevel(datum.getServiceLevel());
                    data.setBufferServiceLevelFactor(datum.getBufferServiveLevelFactor());
                    data.setServiceLevelFactor(datum.getServiceLevelFactor());
                    data.setBufferServiceLevel(datum.getBufferServiceLevel());
                    data.setPrePaidSafetyInventoryCore(datum.getPrePaidSafetyInventoryCore());
                    data.setBufferAverageCore(datum.getBufferAverageCore());
                    data.setBufferSafetyInventoryCore(datum.getBufferSafetyInventoryCore());
                    data.setSafeInvManualConfig(datum.getSafeInvManualConfig());
                    data.setSafetyInventoryCore(datum.getSafetyInventoryCore());
                    data.setTurnoverWeekReservedAvgCore(datum.getTurnoverWeekReservedAvgCore());
                    data.setTurnoverInv(datum.getTurnoverInv());
                    data.setTurnoverWeekPeakCore(datum.getTurnoverWeekPeakCore());
                    result.add(data);
                }
            }
        }
        if (ListUtils.isNotEmpty(result)) {
            ckcldDBHelper.insertBatchWithoutReturnId(result);
        }

    }

    @TaskLog(taskName = "genCDBServiceLevelData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Override
    public void genCDBServiceLevelData(String statTime) {
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_cdb_service_level_data_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);

        //获取原始数据
        List<DuandaoduanServiceLevelDO> all = arch2DBHelper.getAll(DuandaoduanServiceLevelDO.class, "where fdate = ?",
                statTime.replace("-",""));

        //获取zone信息表
        List<StaticZoneDO> allZone = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(allZone, StaticZoneDO::getZoneName, o -> o);
        List<DwsCdbServiceLevelDataDO> result = new ArrayList<>();
        for (DuandaoduanServiceLevelDO item : all) {
            DwsCdbServiceLevelDataDO data = new DwsCdbServiceLevelDataDO();
            data.setStatTime(statTime);
            data.setZoneName(item.getZonename());
            //区域
            data.setProductType(item.getCdbtype());
            //其他地域信息
            if (!StringUtils.isEmpty(data.getZoneName())) {
                String zone = data.getZoneName();
                if (zone.contains("拼多多")) {
                    zone = zone.replace("拼多多-", "");
                }
                StaticZoneDO strategyZone = zoneMap.get(zone);
                if (strategyZone != null) {
                    data.setRegionName(strategyZone.getRegionName());
                    data.setAreaName(strategyZone.getAreaName());
                    data.setCustomhouseTitle(strategyZone.getCustomhouseTitle());
                }
            }
            if (data.getCustomhouseTitle() == null) {
                data.setCustomhouseTitle("(空值)");
            }
            if (data.getRegionName() == null) {
                data.setRegionName("(空值)");
            }
            if (data.getAreaName() == null) {
                data.setAreaName("(空值)");
            }
            //数值信息
            data.setMaxSellMem(item.getMaxSellMem());
            data.setRestMem(item.getX1RealRestMem());
            data.setRestDisk(item.getX1RealRestDiskG());
            data.setSucCnt(item.getLess15MinCnt());
            data.setTotalCnt(item.getTotalCnt());
            data.setMachineCnt(item.getMachineCnt());
            data.setMinRestMem(item.getMinRestMemG());
            result.add(data);
        }
        if (ListUtils.isNotEmpty(result)) {
            ckcldDBHelper.insertBatchWithoutReturnId(result);
        }
    }

    @TaskLog(taskName = "genCBSServiceLevelData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Override
    public void genCBSServiceLevelData(String statTime) {
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_cbs_service_level_data_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);
        //获取原始数据
        List<CbsServiceLevelProcessedRawDataDO> all = cbshawkeyeDBHelper.getAll(CbsServiceLevelProcessedRawDataDO.class,
                "where operation_date = ?", statTime);
        //获取appid信息
        List<Integer> appidList = all.stream().map(CbsServiceLevelProcessedRawDataDO::getAppid)
                .collect(Collectors.toList());
        String appidSql = "select distinct appid, customer_short_name from dwd_txy_appid_info_cf where appid in (?)";
        List<AppidAndNameDTO> temp = ckcldStdCrpDBHelper.getRaw(AppidAndNameDTO.class, appidSql, appidList);
        Map<String, String> appidMap = ListUtils.toMap(temp, o -> String.valueOf(o.getAppid()),
                AppidAndNameDTO::getCustomerShortName);
        //填充国家，可用区等信息
        List<StaticZoneDO> allZone = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(allZone, StaticZoneDO::getZoneName, o -> o);
        Map<String, String> zoneToCountry = dictService.getZoneToCountryMap();
        List<DwsCbsServiceLevelDataDfDO> result = new ArrayList<>();
        for (CbsServiceLevelProcessedRawDataDO data : all) {
            DwsCbsServiceLevelDataDfDO item = DwsCbsServiceLevelDataDfDO.genBasicData(data, zoneMap, zoneToCountry, appidMap);
            result.add(item);
        }
        ckcldDBHelper.insertBatchWithoutReturnId(result);
    }

    @TaskLog(taskName = "genClsLogServiceLevelData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Override
    public void genClsLogServiceLevelData(ClsLogProductEnum productEnum, String statTime) {
        LocalDate localDate;
        if (statTime == null){
            localDate = LocalDate.now().plusDays(-1);
        }else {
            localDate = DateUtils.parseLocalDate(statTime);
        }
        statTime = DateUtils.formatDate(localDate);
        String yearMonth = ReportDateUtils.getYearMonth(localDate);
        // 1. 查询 cls 日志服务水平信息
        List<ClsLogInfo> clsLogInfos = clsLogService.queryClsLog(productEnum, localDate);
        // 2. 清洗策略表准备
        List<TxyRegionInfoDTO> allTxyRegionInfo = dictService.getAllTxyRegionInfo();
        Map<String, TxyRegionInfoDTO> regionMap = new HashMap<>();
        allTxyRegionInfo.forEach(item-> regionMap.put(item.getRegionCode(), item));
        // 3. 转换 + 清洗
        List<ClsLogServiceLevelDiDO> saveData = new ArrayList<>();
        String createTime = DateUtils.format(LocalDateTime.now());
        clsLogInfos.forEach(item->{
            ClsLogServiceLevelDiDO e = new ClsLogServiceLevelDiDO();
            e.setStatTime(localDate);
            e.setCreateTime(createTime);
            e.setNetworkProduct(productEnum.name());
            e.setYearMonth(yearMonth);
            e.setRegionCode(item.getRegion());
            TxyRegionInfoDTO regionInfo = regionMap.get(item.getRegion());
            if (regionInfo != null){
                e.setRegionName(regionInfo.getRegionName());
                e.setAreaName(regionInfo.getAreaName());
                e.setCountryName(regionInfo.getCountry());
                e.setCustomhouseTitle(regionInfo.getCustomhouseTitle());
            }else {
                log.error(String.format("regionCode: %s 对应的 regionInfo 为空", item.getRegion()));
            }
            e.setTotal(item.getTotal());
            if (productEnum == ClsLogProductEnum.CLB){
                e.setInternalFailed(item.getInternalFailed());
                e.setInsufficientResourceFailed(item.getInsufficientResourceFailed());
            }else {
                e.setInternalFailed(item.getInternalError());
                e.setInsufficientResourceFailed(item.getResourceInsufficient());
            }
            saveData.add(e);
        });
        // 4. 删除数据
        CkDBUtils.deletePartitionMultiField(ckcldStdCrpDBHelper, ClsLogServiceLevelDiDO.class, statTime, productEnum.name());
        // 5. 写入数据
        WhereSQL where = new WhereSQL();
        where.and("stat_time = ?", statTime);
        where.and("network_product = ?", productEnum.name());
        CkDBUtils.syncSaveBatch(ckcldStdCrpDBHelper, saveData, 30, where, true);
    }

    @TaskLog(taskName = "genEndToEndMonthData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @Override
    public void genEndToEndMonthData(String yearMonth, String start, String end) {
        String endToEndSql = ORMUtils.getSql(
                "/sql/operation_view/inventory_health/inventory_overview/end_to_end_total_scale_month_trend.sql");
        List<EndToEndMonthDTO> all = planReportDBHelper.getRaw(EndToEndMonthDTO.class, endToEndSql, start, end);
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_end_to_end_zone_device_model_month_data_local ON CLUSTER default_cluster DROP PARTITION ?", yearMonth);
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        Map<String, String> map = ListUtils.toMap(allCvmType, CvmType::getDeviceType, CvmType::getInstanceType);
        List<DwsEndToEndZoneDeviceModelMonthDataDO> result = new ArrayList<>();
        for (EndToEndMonthDTO endToEndMonthDTO : all) {
            DwsEndToEndZoneDeviceModelMonthDataDO item = DwsEndToEndZoneDeviceModelMonthDataDO.genData(
                    endToEndMonthDTO, map, yearMonth);
            result.add(item);
        }
        ckcldDBHelper.insertBatchWithoutReturnId(result);
    }


    /**
     *  监控配置在cloud_demand_common库的schedule_task表中
     */
    @Override
    @TaskLog(taskName = "genCRSServiceLevelData")
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    public void genCRSServiceLevelData(String statTime) {
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_crs_service_level_data_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
        //获取原始数据
        List<CRSDuandaoduanServiceLevelDO> all = dialsystemDBHelper.getAll(CRSDuandaoduanServiceLevelDO.class,
                "where update_time = ?", statTime);

        //获取地理配置信息
        Map<String, String> zoneToCountryMap = dictService.getZoneToCountryMap();
        List<StaticZoneDO> allZoneInfos = dictService.getAllZoneInfos();
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(allZoneInfos, StaticZoneDO::getZoneName, o -> o);
        List<DwsCrsServiceLevelDataDO> result = new ArrayList<>();
        for (CRSDuandaoduanServiceLevelDO item : all) {
            DwsCrsServiceLevelDataDO data = new DwsCrsServiceLevelDataDO();
            data.setZoneName(item.getZonename());
            data.setRegionName(item.getRegionname());
            data.setStatTime(item.getUpdateTime().toString());
            data.setMaxSellMem(item.getMaxSellMem());
            data.setRestMem(item.getX1RealRestMem());
            data.setSucCnt(item.getLess15MinCnt());
            data.setTotalCnt(item.getTotalCnt());
            data.setMachineCnt(item.getMachineCnt());
            data.setMinRestMem(item.getMinRestMemG());
            StaticZoneDO staticZoneDO = zoneMap.get(data.getZoneName());
            if (staticZoneDO != null) {
                data.setAreaName(staticZoneDO.getAreaName());
                data.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
            }else {
                data.setAreaName("(空值)");
                data.setCustomhouseTitle("(空值)");
            }
            String country = zoneToCountryMap.get(data.getZoneName());
            data.setCountry(StringUtils.isEmpty(country) ? "(空值)" : country);
            result.add(data);
        }
        if (ListUtils.isNotEmpty(result)) {
            ckcldDBHelper.insertBatchWithoutReturnId(result);
        }
    }

    @Override
    public void genTDSQLServiceLevelData(String statTime) {
        //1.从t_cynosdb_duandaoduan_service_level获取地域、磁盘、内存容量相关信息
        String fdate = statTime.replace("-", "");
        List<CynosdbDuandaoduanServiceLevelDO> succList = arch2DBHelper.getAll(CynosdbDuandaoduanServiceLevelDO.class,
                "where fdate = ?", fdate);
        //2.从cynosstockinfo获取售罄规格信息
        String createDate = statTime + " 00:00:00";
        List<CynosstockinfoDO> soldOutList = tdsqlresourceDBHelper.getAll(CynosstockinfoDO.class, "where createDate = ? and masterHasStock = 0 and machineType in ('exclusive', 'common')", createDate);
        Map<String, List<CynosstockinfoDO>> soldOutMap = ListUtils.toMapList(soldOutList,
                CynosstockinfoDO::getMasterZone, o -> o);
        //3.从cynos_applyset_info获取出售量信息
        List<CynosApplysetInfoDO> numList = tdsqlresourceDBHelper.getAll(CynosApplysetInfoDO.class, "where createDate = ? and xtype in ('exclusive', 'common')", statTime);
        Map<String, List<CynosApplysetInfoDO>> numMap = ListUtils.toMapList(numList, CynosApplysetInfoDO::getZone, o -> o);
        //4.从cynos_applyset_success获取发货成功率的信息
        List<CynosApplysetSuccessDO> sucList = tdsqlresourceDBHelper.getAll(CynosApplysetSuccessDO.class,
                "where createDate = ?", statTime);
        Map<String, List<CynosApplysetSuccessDO>> sucMap = ListUtils.toMapList(sucList,
                CynosApplysetSuccessDO::getZone, o -> o);
        List<DwsTdsqlServiceLevelDataDfDO> result = new ArrayList<>();
        List<StaticZoneDO> allZoneInfos = dictService.getAllZoneInfos();
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(allZoneInfos, StaticZoneDO::getZone, o -> o);
        Map<String, String> zoneToCountryMap = dictService.getZoneToCountryMap();
        for (CynosdbDuandaoduanServiceLevelDO item : succList) {
            DwsTdsqlServiceLevelDataDfDO data = item.genTdsqlData(statTime);
            if (data.getMinRestMem() != null) {
                //设置区域信息
                StaticZoneDO staticZoneDO = zoneMap.get(item.getZonename());
                if (staticZoneDO != null) {
                    data.setZoneName(staticZoneDO.getZoneName());
                    data.setRegionName(staticZoneDO.getRegionName());
                    data.setAreaName(staticZoneDO.getAreaName());
                    data.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
                    data.setRegionCode(staticZoneDO.getApiRegion());
                }else {
                    data.setZoneName("(空值)");
                    data.setRegionName("(空值)");
                    data.setAreaName("(空值)");
                    data.setCustomhouseTitle("(空值)");
                    data.setRegionCode("(空值)");
                }
                String country = zoneToCountryMap.get(data.getZoneName());
                data.setCountry(StringUtils.isEmpty(country) ? "(空值)" : country);

                //获取成功率数据
                List<CynosApplysetSuccessDO> collect = sucMap.get(data.getZoneCode());
                int total = NumberUtils.sum(collect, CynosApplysetSuccessDO::getTotalNum).intValue();
                int fail = NumberUtils.sum(collect, CynosApplysetSuccessDO::getFailNum).intValue();
                data.setTotalCnt(total);
                data.setSucCnt(total - fail);

                //获取售罄数据
                List<CynosApplysetInfoDO> soldTotalList = numMap.get(data.getZoneCode());
                data.setSoldCnt(NumberUtils.sum(soldTotalList, CynosApplysetInfoDO::getCount).intValue());
                List<CynosstockinfoDO> outList = soldOutMap.get(data.getZoneCode());
                if (ListUtils.isNotEmpty(outList)) {
                    List<String> machineList = outList.stream()
                            .map(o -> String.join("@", o.getMachineType(), o.getCpu(), o.getMemory())).distinct().collect(
                                    Collectors.toList());
                    if (ListUtils.isNotEmpty(soldTotalList)) {
                        List<CynosApplysetInfoDO> soldOut = soldTotalList.stream().filter(o -> {
                            String join = String.join("@", o.getXtype(), String.valueOf(o.getCpu()),
                                    String.valueOf(o.getMem()));
                            return machineList.contains(join);
                        }).collect(Collectors.toList());
                        data.setSoldOutCnt(NumberUtils.sum(soldOut, CynosApplysetInfoDO::getCount).intValue());
                    }else {
                        data.setSoldOutCnt(0);
                    }
                }else {
                    data.setSoldOutCnt(0);
                }
                result.add(data);
            }
        }

        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.dws_tdsql_service_level_data_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);

        if (ListUtils.isNotEmpty(result)) {
            ckcldDBHelper.insertBatchWithoutReturnId(result);
        }
    }

    //针对科天的表进行增量同步
    @Override
    public void synchronizeStaticCvmType() {
        List<StaticCvmtypeDO> all = cdCommonDbHelper.getAll(StaticCvmtypeDO.class);
        List<StaticGinsfamilyDO> ginAll = cdCommonDbHelper.getAll(StaticGinsfamilyDO.class);
        all = all.stream().filter(o -> !StringUtils.isEmpty(o.getGinsfamily())).collect(Collectors.toList());
        Map<String, StaticCvmtypeDO> deviceMap = ListUtils.toMap(all, StaticCvmtypeDO::getCvmtype, o -> o);
        Map<String, StaticGinsfamilyDO> ginMap = ListUtils.toMap(ginAll, StaticGinsfamilyDO::getGinsfamily, o -> o);
        List<CloudDemandCsigDeviceExtendInfoDO> list = new ArrayList<>();
        for (Entry<String, StaticCvmtypeDO> entry : deviceMap.entrySet()) {
            StaticCvmtypeDO value = entry.getValue();
            CloudDemandCsigDeviceExtendInfoDO item = new CloudDemandCsigDeviceExtendInfoDO();
            item.setDeviceType(value.getCvmtype());
            item.setInstanceTypeEng(value.getGinsfamily());
            StaticGinsfamilyDO staticGinsfamilyDO = ginMap.get(value.getGinsfamily());
            if (staticGinsfamilyDO != null) {
                if (staticGinsfamilyDO.getGinsfamilyName().contains("[历史遗留]")) {
                    item.setInstanceType(staticGinsfamilyDO.getGinsfamilyName().replace("[历史遗留]", ""));
                }else {
                    item.setInstanceType(staticGinsfamilyDO.getGinsfamilyName());
                }
                if (staticGinsfamilyDO.getGpuChipPerCard() > 0) {
                    item.setProduct("GPU");
                }else if (staticGinsfamilyDO.getBiztype().equals("cvm")) {
                    item.setProduct("CVM");
                }else if (staticGinsfamilyDO.getBiztype().equals("baremetal")) {
                    item.setProduct("裸金属");
                }
            }
            item.setSaleCore(BigDecimal.valueOf(value.getCpuOrigin()/100));
            list.add(item);
        }
        List<CloudDemandCsigDeviceExtendInfoDO> infoAll = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        List<String> infoDevice = infoAll.stream().map(CloudDemandCsigDeviceExtendInfoDO::getDeviceType).distinct()
                .collect(Collectors.toList());
        list = list.stream().filter(o -> !infoDevice.contains(o.getDeviceType())).collect(Collectors.toList());
        yuntiDBHelper.insertOrUpdate(list);

    }

    @Data
    public static class EndToEndMonthDTO {


        @Column("zone_name")
        private String zoneName;

        @Column("region_name")
        private String regionName;

        @Column("area_name")
        private String areaName;

        @Column("customhouse_title")
        private String customhouseTitle;


        @Column("device_type")
        private String deviceType;

        @Column("category")
        private String category;

        @Column("sale_type")
        private String saleType;

        @Column("item")
        private String item;

        @Column("core_num")
        private BigDecimal coreNum;

    }

    public List<DwsApiSuccessDataDfLocalDO> getEMRApiSuccessData(String unix, String statTime) {
        HttpHeaders header = new HttpHeaders();
        header.add("Accept", "application/json");
        header.add("Content-Type", "application/x-www-form-urlencoded");
        header.add("Authorization",
                "Bearer eyJrIjoiME9OQjBBUHlkRkhJMkNrY1RGMW56NjhSd1dmbk43c3EiLCJuIjoiZW1yIiwiaWQiOjF9");
        MultiValueMap<String, String> failParams = new LinkedMultiValueMap<>();
        MultiValueMap<String, String> totalParams = new LinkedMultiValueMap<>();
        String totalBody = "sum(emrcc_api_total_core_count{}) by (parentSpec, zone, spec, uin)";
        String failBody = "sum(emrcc_api_fail_core_count{}) by (parentSpec, zone, spec, uin)";
        failParams.add("query", failBody);
        failParams.add("time", unix);
        totalParams.add("query", totalBody);
        totalParams.add("time", unix);
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<MultiValueMap<String, String>> failEntity = new HttpEntity<>(failParams, header);
        HttpEntity<MultiValueMap<String, String>> totalEntity = new HttpEntity<>(totalParams, header);
        String failResult = restTemplate.postForObject(emrUrl, failEntity, String.class);
        String totalResult = restTemplate.postForObject(emrUrl, totalEntity, String.class);
        EMRJson failJson = JSON.parseObject(failResult, EMRJson.class);
        EMRJson totalJson = JSON.parseObject(totalResult, EMRJson.class);
        List<Item> failData = new ArrayList<>();
        List<Item> totalData = new ArrayList<>();
        if (failJson != null) {
            failData = failJson.getData().getResult();;
        }
        if (totalJson != null) {
            totalData = totalJson.getData().getResult();
        }
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, String> zoneMap = ListUtils.toMap(all, o -> o.getZone(), o -> o.getZoneName());
        //将成功数与总数合并
        List<DwsApiSuccessDataDfLocalDO> merge = ListUtils.merge(failData, totalData, o1 -> o1.getMetric(),
                o2 -> o2.getMetric(), (o1, o2) -> {
                    DwsApiSuccessDataDfLocalDO temp = new DwsApiSuccessDataDfLocalDO();
                    if (ListUtils.isNotEmpty(o1)) {
                        String metric = o1.get(0).getMetric();
                        JSONObject object = JSON.parseObject(metric);
                        temp.setInstanceFamily(object.getString("parentSpec"));
                        String zone = object.getString("zone");
                        if (zone != null) {
                            temp.setZoneName(zoneMap.get(zone));
                        }
                        temp.setUin(object.getLong("uin"));
                        temp.setInstanceType(object.getString("spec"));
                    } else {
                        String metric = o2.get(0).getMetric();
                        JSONObject object = JSON.parseObject(metric);
                        temp.setInstanceFamily(object.getString("parentSpec"));
                        String zone = object.getString("zone");
                        if (zone != null) {
                            temp.setZoneName(zoneMap.get(zone));
                        }
                        temp.setUin(object.getLong("uin"));
                        temp.setInstanceType(object.getString("spec"));
                    }
                    temp.setProductType("EMR");
                    temp.setStatTime(statTime);
                    BigDecimal apiFail = BigDecimal.ZERO;
                    if (ListUtils.isNotEmpty(o1)) {
                        apiFail = o1.get(0).getValue().get(1) == null ? BigDecimal.ZERO : o1.get(0).getValue().get(1);
                    }
                    BigDecimal total = BigDecimal.ZERO;
                    if (ListUtils.isNotEmpty(o2)) {
                        total = o2.get(0).getValue().get(1) == null ? BigDecimal.ZERO : o2.get(0).getValue().get(1);
                    }
                    temp.setSuccNum(total.subtract(apiFail));
                    temp.setTotalNum(total);
                    return temp;
                });
        return merge;
    }

    private String toKey(DwsInventoryHealthWeeklyScaleDfDO d) {
        return StringTools.join("@", d.getProductType(),
                d.getCustomhouseTitle(), d.getAreaName(), d.getRegionName(), d.getZoneName(),
                d.getInstanceType(), d.getCustomerCustomGroup());
    }

    private List<DwsInventoryHealthWeeklyScaleDfDO> getLastWeekPeakData(String statTime) {
        ResPlanHolidayWeekDO lastWeek = getLastWeek(statTime);

        // 1. 先拿上周周净峰
        WhereSQL peakCondition = new WhereSQL();
        peakCondition.and("stat_time = ?", statTime);
        peakCondition.and("holiday_year = ?", lastWeek.getYear());
        peakCondition.and("holiday_week = ?", lastWeek.getWeek());
        peakCondition.and("product_type = ?", "CVM");
        peakCondition.and("customer_custom_group = ?", "ALL");
        peakCondition.and("exclude_uin_list = ?", "(空值)");
        List<DwsInventoryHealthWeeklyScaleDfDO> all = ckcldDBHelper.getAll(
                DwsInventoryHealthWeeklyScaleDfDO.class, peakCondition.getSQL(), peakCondition.getParams());

        return all;
    }

    private ResPlanHolidayWeekDO getLastWeek(String statTime) {
        Date lastWeekDay = DateUtils.addTime(DateUtils.parse(statTime),  Calendar.DATE, -7);
        return dictService.getHolidayWeekInfoByDate(DateUtils.formatDate(lastWeekDay));
    }

    private List<DwsDemandWeekNPplVersionItemDO> handlePplData(
            String statTime,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            List<PplVersionItemDO> pplVersionItemDOS,
            int n,
            Map<String, List<InventoryHealthMainZoneNameConfigDO>> allMainZoneNameConfigMap,
            boolean filterByHolidayWeekInfo
    ) {
        List<DwsDemandWeekNPplVersionItemDO> result = ListUtils.newList();
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();

        for (PplVersionItemDO itemDO : pplVersionItemDOS) {
            // 均摊到每一周
            DwsDemandWeekNPplVersionItemDO dwsDemandWeekNPplVersionItemDO = DwsDemandWeekNPplVersionItemDO.from(itemDO);
            // 拿到开始购买时间和结束购买时间
            LocalDate beginBuyDate = LocalDate.parse(itemDO.getBeginBuyDate());
            LocalDate endBuyDate = LocalDate.parse(itemDO.getEndBuyDate());

            if (filterByHolidayWeekInfo) {
                LocalDate wdoEndDate = LocalDate.parse(holidayWeekInfoDTO.getEndDate());
                LocalDate wdoStartDate = LocalDate.parse(holidayWeekInfoDTO.getStartDate());
                // 跳过不落在给定周内的数据
                if (
                        !((!beginBuyDate.isBefore(wdoStartDate) && !beginBuyDate.isAfter(wdoEndDate)) ||
                                (!endBuyDate.isBefore(wdoStartDate) && !endBuyDate.isAfter(wdoEndDate)) ||
                                (beginBuyDate.isBefore(wdoStartDate) && endBuyDate.isAfter(wdoEndDate)))
                ) {
                    continue;
                }
            }

            // 中长尾预测，按周均摊
            if (itemDO.getSource().equals("FORECAST")) {
                // PPL 预测年月的周数量
                long weekNum = all.stream().filter(item -> item.getYear().equals(itemDO.getYear()) && item.getMonth().equals(itemDO.getMonth())).count();
                // 按照节假周的数量均摊到周
                dwsDemandWeekNPplVersionItemDO.setAverageTotalCore(itemDO.getTotalCore().divide(BigDecimal.valueOf(weekNum), 2, BigDecimal.ROUND_HALF_UP));
            } else {
                // 找到所有 beginBuyDate 和 endBuyDate 横跨的周信息
                List<ResPlanHolidayWeekDO> dtos = Lang.list();

                for (int i = 0; i < all.size(); i++) {
                    ResPlanHolidayWeekDO wdo = all.get(i);
                    LocalDate wdoEndDate = LocalDate.parse(wdo.getEnd());
                    LocalDate wdoStartDate = LocalDate.parse(wdo.getStart());

                    if (
                            (!beginBuyDate.isBefore(wdoStartDate) && !beginBuyDate.isAfter(wdoEndDate)) ||
                                    (!endBuyDate.isBefore(wdoStartDate) && !endBuyDate.isAfter(wdoEndDate)) ||
                                    (beginBuyDate.isBefore(wdoStartDate) && endBuyDate.isAfter(wdoEndDate))
                    ) {
                        dtos.add(wdo);
                    }
                }

                if (dtos.size() > 0) {
                    // 直接按照所跨的周均摊。这里只拿了当周的，下一周的均摊会在下一个版本处理，如果下一个版本没有继承这条预测，那么下周将不会有这一条的均摊
                    dwsDemandWeekNPplVersionItemDO.setAverageTotalCore(itemDO.getTotalCore().divide(BigDecimal.valueOf(dtos.size()), 2, BigDecimal.ROUND_HALF_UP));
                }
            }

            dwsDemandWeekNPplVersionItemDO.setStatTime(LocalDate.parse(statTime));
            dwsDemandWeekNPplVersionItemDO.setHolidayYear(holidayWeekInfoDTO.getYear());
            dwsDemandWeekNPplVersionItemDO.setHolidayMonth(holidayWeekInfoDTO.getMonth());
            dwsDemandWeekNPplVersionItemDO.setHolidayWeek(holidayWeekInfoDTO.getWeek());
            dwsDemandWeekNPplVersionItemDO.setWeekStartDate(holidayWeekInfoDTO.getStartDate());
            dwsDemandWeekNPplVersionItemDO.setWeekEndDate(holidayWeekInfoDTO.getEndDate());
            dwsDemandWeekNPplVersionItemDO.setWeekN(n);
            dwsDemandWeekNPplVersionItemDO.setWeekIndex(holidayWeekInfoDTO.getWeekNFromNow());
            // 暂时支持 CVM
            dwsDemandWeekNPplVersionItemDO.setProductType("CVM");

            // 对于随机可用区，均摊到所在城市的所有主力可用区
            if (dwsDemandWeekNPplVersionItemDO.getZoneName().equals("随机可用区")) {
                List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOS = allMainZoneNameConfigMap.get(dwsDemandWeekNPplVersionItemDO.getRegionName());

                if (zoneNameConfigDOS != null) {
                    // 按照园区分类分组，主力可用区在前面
                    Map<String,  List<InventoryHealthMainZoneNameConfigDO>> groupedZoneNameConfigDOS = ListUtils.toMapList(zoneNameConfigDOS, o -> o.getType(), o -> o);
                    // 按照如下顺序排序匹配
                    List<String> orderedTypes = ListUtils.newList("PRINCIPAL", "SECONDARY", "WITHDRAWING", "WITHDRAW", "SPECIAL");
                    // 找到第一个命中的可用区分类
                    List<InventoryHealthMainZoneNameConfigDO> mainZoneNameConfigDOS = ListUtils.newList();

                    for (String orderedType : orderedTypes) {
                        List<InventoryHealthMainZoneNameConfigDO> typeZoneNameConfigDOS = groupedZoneNameConfigDOS.get(orderedType);
                        if (!ListUtils.isNotEmpty(typeZoneNameConfigDOS)) {
                            mainZoneNameConfigDOS = typeZoneNameConfigDOS;
                            break;
                        }
                    }

                    BigDecimal avgTotalCore = dwsDemandWeekNPplVersionItemDO.getTotalCore().divide(new BigDecimal(mainZoneNameConfigDOS.size()));
                    BigDecimal avgAverageTotalCore = dwsDemandWeekNPplVersionItemDO.getAverageTotalCore().divide(new BigDecimal(mainZoneNameConfigDOS.size()));

                    for (InventoryHealthMainZoneNameConfigDO mainZoneNameConfigDO : mainZoneNameConfigDOS) {
                        // clone 一个新对象
                        try {
                            DwsDemandWeekNPplVersionItemDO cloned = dwsDemandWeekNPplVersionItemDO.clone();
                            cloned.setAverageTotalCore(avgAverageTotalCore);
                            cloned.setTotalCore(avgTotalCore);
                            cloned.setAreaName(mainZoneNameConfigDO.getAreaName());
                            cloned.setZoneName(mainZoneNameConfigDO.getZoneName());
                        } catch (CloneNotSupportedException e) {
                            throw BizException.makeThrow("均摊随机可用区时，复制对象失败，联系 brightwwu 处理" + e);
                        }
                    }
                } else {
                    // 城市不在园区配置中，告警
                    this.taskLogService.genRunWarnLog("genWeekNForecastData", "genWeekNForecastData", dwsDemandWeekNPplVersionItemDO.getRegionName() + " 不在园区配置中，无法均摊");
                }
            } else {
                result.add(dwsDemandWeekNPplVersionItemDO);
            }
        }

        return result;
    }


    /**
     * 基于未来预测数据生成
     * 不支持uin及客户名称的自主剔除; 不支持计费类型筛选;
     * 2023-06-07:扩展客户类型到头部
     */
    public void genFuturePartData(String statTime, Integer futureWeekSpanNums) {
        long count = ckcldDBHelper.getCount(DwsInventoryHealthWeeklyScaleDfDO.class,
                "where stat_time = ? and week_index >= 0", statTime);
        if (count == 0) {
            List<DwsInventoryHealthWeeklyScaleDfDO> weekPeakData = genFutureWeekPeakData(statTime, futureWeekSpanNums);
            ckcldDBHelper.insertBatchWithoutReturnId(weekPeakData);
            log.info("日期: {} 的未来周峰数据已插入, 条数: {}", statTime, weekPeakData.size());
            taskLogService.genRunWarnLog("genSafetyInventoryData", "genFuturePartData",
                    "日期: {" + statTime + "}的未来周峰数据已插入, 条数: {"+weekPeakData.size() + "}");
        }
    }

    /**
     * 历史：周峰、周净增两种算法的安全库存底数生成
     * 作为定时任务跑固化名单客户时：uins和isPositive都为空
     * 作为页面试验性筛选时:
     *      ListUtils.isNotEmpty(uins) && isPositive == true， 正向过滤uins
     *      ListUtils.isNotEmpty(uins) && isPositive == false, 反向剔除uins
     * 客户简称同理
     */
    public void genHistoryPartData(String statTime, List<String> uins,
            List<String> customerNames, Boolean isPositive,
            Integer historyWeekSpanNums){
        //  根据不同的客户类型group，合并相同的k并插入db中
        CustomerCustomGroupEnum[] groupEnums = CustomerCustomGroupEnum.getHistoryAlgorithmList();
        for (CustomerCustomGroupEnum each : groupEnums) {
            List<String> customerTabTypeList = CustomerCustomGroupEnum.getCustomerTabType(each);
            //  历史周峰
            List<DwsInventoryHealthWeeklyScaleDfDO> weekPeakData =
                    genHistoryWeekPeakData(statTime, uins, customerNames, isPositive, historyWeekSpanNums, customerTabTypeList);
            //  历史周净增
            List<DwsInventoryHealthWeeklyScaleDfDO> weekDiffData =
                    genHistoryWeekDiffData(statTime, uins, customerNames, isPositive, historyWeekSpanNums, customerTabTypeList);

            List<DwsInventoryHealthWeeklyScaleDfDO> result = ListUtils.merge(weekPeakData, weekDiffData,
                    o1 -> o1.getGroupK(), o2 -> o2.getGroupK(),
                    (o1, o2) -> {
                        DwsInventoryHealthWeeklyScaleDfDO one = new DwsInventoryHealthWeeklyScaleDfDO();
                        one.setStatTime(LocalDate.parse(statTime));
                        one.setCustomerCustomGroup(each.getName());
                        if (ListUtils.isNotEmpty(uins)){
                            one.setExcludeUinList(OperationViewReq2.handleExcludeUinList(uins));
                        }
                        //  公共属性填充
                        if (ListUtils.isNotEmpty(o1)) {
                            one.setHolidayYear(o1.get(0).getHolidayYear());
                            one.setHolidayMonth(o1.get(0).getHolidayMonth());
                            one.setHolidayWeek(o1.get(0).getHolidayWeek());
                            one.setHolidayWeekStartDate(o1.get(0).getHolidayWeekStartDate());
                            one.setHolidayWeekEndDate(o1.get(0).getHolidayWeekEndDate());
                            one.setWeekIndex(o1.get(0).getWeekIndex());
                            one.setProductType(o1.get(0).getProductType());
                            one.setInstanceType(o1.get(0).getInstanceType());
                            one.setCustomhouseTitle(o1.get(0).getCustomhouseTitle());
                            one.setAreaName(o1.get(0).getAreaName());
                            one.setRegionName(o1.get(0).getRegionName());
                            one.setZoneName(o1.get(0).getZoneName());
                        } else {
                            one.setHolidayYear(o2.get(0).getHolidayYear());
                            one.setHolidayMonth(o2.get(0).getHolidayMonth());
                            one.setHolidayWeek(o2.get(0).getHolidayWeek());
                            one.setHolidayWeekStartDate(o2.get(0).getHolidayWeekStartDate());
                            one.setHolidayWeekEndDate(o2.get(0).getHolidayWeekEndDate());
                            one.setWeekIndex(o2.get(0).getWeekIndex());
                            one.setProductType(o2.get(0).getProductType());
                            one.setInstanceType(o2.get(0).getInstanceType());
                            one.setCustomhouseTitle(o2.get(0).getCustomhouseTitle());
                            one.setAreaName(o2.get(0).getAreaName());
                            one.setRegionName(o2.get(0).getRegionName());
                            one.setZoneName(o2.get(0).getZoneName());
                        }
                        //  值填充
                        if (ListUtils.isNotEmpty(o1)) {
                            one.setWeekPeakLogicNum(o1.get(0).getWeekPeakLogicNum());
                        } else {
                            one.setWeekPeakLogicNum(BigDecimal.ZERO);
                        }
                        if (ListUtils.isNotEmpty(o2)) {
                            one.setWeekDiffLogicNum(o2.get(0).getWeekDiffLogicNum());
                        } else {
                            one.setWeekDiffLogicNum(BigDecimal.ZERO);
                        }
                        // 填充历史周峰数据
                        one.setWeekPeakServiceLogicNum(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakServiceLogicNum() : BigDecimal.ZERO);
                        one.setWeekPeakServiceLogicNumAvg12(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakServiceLogicNumAvg12() : BigDecimal.ZERO);
                        one.setWeekPeakServiceLogicNumAvg13(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakServiceLogicNumAvg13() : BigDecimal.ZERO);
                        one.setWeekPeakBillLogicNum(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakBillLogicNum() : BigDecimal.ZERO);
                        one.setWeekPeakBillLogicNumAvg12(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakBillLogicNumAvg12() : BigDecimal.ZERO);
                        one.setWeekPeakBillLogicNumAvg13(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakBillLogicNumAvg13() : BigDecimal.ZERO);
                        one.setWeekPeakLogicNumAvg12(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakLogicNumAvg12() : BigDecimal.ZERO);
                        one.setWeekPeakLogicNumAvg13(ListUtils.isNotEmpty(o1) ? o1.get(0).getWeekPeakLogicNumAvg13() : BigDecimal.ZERO);
                        return one;
                    });
            ckcldDBHelper.insertBatchWithoutReturnId(result);
            log.info("日期: {} 的历史数据 - {} 自定义组合已插入, 条数: {}", statTime, each.getName(), result.size());
            taskLogService.genRunWarnLog("genSafetyInventoryData", "genFuturePartData",
                    "日期: {" + statTime + "}的历史数据组合{" + each.getName() + "}已插入, 条数: {"+weekPeakData.size() + "}");
        }

    }


    /**
     * 历史-周峰安全库存底数生成
     */
    private List<DwsInventoryHealthWeeklyScaleDfDO> genHistoryWeekPeakData(String statTime, List<String> uins,
            List<String> customerNames, Boolean isPositive,
            Integer historyWeekSpanNums,
            List<String> customerTabTypeList){
        //  1、获取历史指定时间区间范围的节假周信息
        LocalDate date = LocalDate.parse(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(date, historyWeekSpanNums);
        if (ListUtils.isEmpty(holidayWeekInfo)) {
            return Lang.list();
        }
        //  获取第一周周一，作为beginDate
        String beginDate = holidayWeekInfo.get(0).getStartDate();
        //  获取最后一周周日，作为endDate
        String lastDate = holidayWeekInfo.get(holidayWeekInfo.size() - 1).getEndDate();
        //  获取一个Map，其中k是当周第一天，v是当周对statTime是第几周
        Map<String, HolidayWeekInfoDTO> yearWeekMap = ListUtils.toMap(holidayWeekInfo, o -> o.getYearWeek(), Function.identity());

        //  2、获取数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/history_week_peak_data.sql");
        HashMap<String, Object> params = new HashMap<>();
        params.put("start", beginDate);
        params.put("end", lastDate);
        //  目前只要CVM的
        params.put("product", "CVM");
        params.put("customerTabTypeList", customerTabTypeList);

        //  针对uin的逻辑
        if (ListUtils.isNotEmpty(uins)){
            if (isPositive != null && isPositive){      //  正向，过滤uins
                sql = sql.replace("${UIN_CONDITION}", "and uin in (:uins)");
                params.put("uins", uins);
            } else if (isPositive != null && !isPositive) {     //  反向，剔除uins
                sql = sql.replace("${UIN_CONDITION}", "and uin not in (:uins)");
                params.put("uins", uins);
            } else {    //  不选无效
                sql = sql.replace("${UIN_CONDITION}", "");
            }
        } else {    //  uin为空无效
            sql = sql.replace("${UIN_CONDITION}", "");
        }

        //  针对客户名称/客户简称的逻辑
        if (ListUtils.isNotEmpty(customerNames)){
            if (isPositive != null && isPositive){      //  正向，过滤uins
                sql = sql.replace("${CUSTOMER_NAME_CONDITION}",
                        "and (customer_name in (:customerNames) or customer_short_name in (:customerNames))");
                params.put("customerNames", customerNames);
            } else if (isPositive != null && !isPositive) {     //  反向，剔除uins
                sql = sql.replace("${CUSTOMER_NAME_CONDITION}",
                        "and (customer_name not in (:customerNames) and customer_short_name not in (:customerNames))");
                params.put("customerNames", customerNames);
            } else {    //  不选无效
                sql = sql.replace("${CUSTOMER_NAME_CONDITION}", "");
            }
        } else {    //  客户简称列表为空无效
            sql = sql.replace("${CUSTOMER_NAME_CONDITION}", "");
        }

        // 3、找到近 13 周，以及近 14-26 周的数据。14-26周的数据用于求近13周的 12/13 周周峰平均值数据
        List<SafetyInventoryHistoryPeakDTO> raw = ckcldStdCrpDBHelper.getRaw(SafetyInventoryHistoryPeakDTO.class, sql, params);
        // 14-26 周的数据
        List<HolidayWeekInfoDTO> holidayWeekInfo14To26 = inventoryHealthDictService.getHolidayWeekInfoBase(LocalDate.parse(beginDate), historyWeekSpanNums);
        List<SafetyInventoryHistoryPeakDTO> raw14To26 = ListUtils.newList();
        if (ListUtils.isNotEmpty(holidayWeekInfo14To26)) {
            params.put("start", holidayWeekInfo14To26.get(0).getStartDate());
            params.put("end", holidayWeekInfo14To26.get(holidayWeekInfo14To26.size() - 1).getEndDate());
            raw14To26 = ckcldStdCrpDBHelper.getRaw(SafetyInventoryHistoryPeakDTO.class, sql, params);
        }
        // 汇总数据，用于求近13周的 12/13 周周峰平均值数据
        List<SafetyInventoryHistoryPeakDTO> rawTotal = raw14To26;
        rawTotal.addAll(raw);
        // 汇总日期，用于求近13周的 12/13 周周峰平均值数据
        List<HolidayWeekInfoDTO> holidayWeekInfoTotal = holidayWeekInfo14To26;
        holidayWeekInfo14To26.forEach(item -> {
            // -1 变成 -14
            item.setWeekNFromNow(item.getWeekNFromNow() + historyWeekSpanNums);
        });
        holidayWeekInfoTotal.addAll(holidayWeekInfo);
        // 按照 year month week 排序，方便查找近 12/13 周
        holidayWeekInfoTotal.sort(Comparator.comparing(HolidayWeekInfoDTO::getWeekNFromNow));

        List<DwsInventoryHealthWeeklyScaleDfDO> result = Lang.list();
        Map<String, List<SafetyInventoryHistoryPeakDTO>> rawTotalMap = ListUtils.toMapList(rawTotal, o -> StringTools.join("@", o.getProduct(), o.getZoneName(), o.getInstanceType()), o -> o);
        //  4、转换生成DO
        ListUtils.forEach(raw, o -> {
            HolidayWeekInfoDTO dto = yearWeekMap.get(o.getYearWeek());
            int dtoTime = dto.getYear() * 100 + dto.getWeek();
            if (dto != null) {
                DwsInventoryHealthWeeklyScaleDfDO one = SafetyInventoryHistoryPeakDTO.transform(o, dto);
                one.setWeekPeakLogicNum(o.getLogicNum());
                one.setWeekPeakBillLogicNum(o.getBillNum());
                one.setWeekPeakServiceLogicNum(o.getServiceNum());

                // 找到 dto 在 holidayWeekInfoTotal 中的下标
                int index = holidayWeekInfoTotal.indexOf(dto);
                // 如果当前 dto 不是 holidayWeekInfoTotal 中最新的那一天（即不是最近的那一周）
                HolidayWeekInfoDTO dto12 = holidayWeekInfoTotal.get(index - 12);
                HolidayWeekInfoDTO dto13 = holidayWeekInfoTotal.get(index - 13);
                int dto12Time = dto12.getYear() * 100 + dto12.getWeek();
                int dto13Time = dto13.getYear() * 100 + dto13.getWeek();

                // 从 rawTotal 中找到 dto12 和 dto13 对应的所有
                List<SafetyInventoryHistoryPeakDTO> raw12 = ListUtils.newList();
                List<SafetyInventoryHistoryPeakDTO> raw13 = ListUtils.newList();
                List<SafetyInventoryHistoryPeakDTO> mapList = rawTotalMap.get(StringTools.join("@", o.getProduct(), o.getZoneName(), o.getInstanceType()));
                // 如果有 year month 重复的条数，只取第一条
                Set<Integer> set = new HashSet<>();
                ListUtils.forEach(mapList, o1 -> {
                    int o1Time = o1.getYear() * 100 + o1.getWeek();
                    if (set.contains(o1Time)) {
                        return;
                    }
                    set.add(o1Time);
                    // 如果在 12 周范围内
                    if (o1Time > dto12Time && o1Time <= dtoTime) {
                        raw12.add(o1);
                    }
                    // 如果在 13 周范围内
                    if (o1Time > dto13Time && o1Time <= dtoTime) {
                        raw13.add(o1);
                    }
                });
                // 不够的补上，确保均值正确
                if (raw12.size() < 12) {
                    int size = 12 - raw12.size();
                    for (int i = 0; i < size; i++) {
                        raw12.add(new SafetyInventoryHistoryPeakDTO());
                    }
                }
                if (raw13.size() < 13 ) {
                    int size = 13 - raw13.size();
                    for (int i = 0; i < size; i++) {
                        raw13.add(new SafetyInventoryHistoryPeakDTO());
                    }
                }

                //  求平均值
                one.setWeekPeakBillLogicNumAvg12(NumberUtils.avg(raw12, 2, item -> item.getBillNum()));
                one.setWeekPeakServiceLogicNumAvg12(NumberUtils.avg(raw12, 2, item -> item.getServiceNum()));
                one.setWeekPeakLogicNumAvg12(NumberUtils.avg(raw12, 2, item -> item.getLogicNum()));
                one.setWeekPeakBillLogicNumAvg13(NumberUtils.avg(raw13, 2, item -> item.getBillNum()));
                one.setWeekPeakServiceLogicNumAvg13(NumberUtils.avg(raw13, 2, item -> item.getServiceNum()));
                one.setWeekPeakLogicNumAvg13(NumberUtils.avg(raw13, 2, item -> item.getLogicNum()));

                result.add(one);
            }
        });
        return result;
    }

    /**
     * 历史-周净增安全库存底数生成
     */
    private List<DwsInventoryHealthWeeklyScaleDfDO> genHistoryWeekDiffData(String statTime, List<String> uins,
            List<String> customerNames, Boolean isPositive,
            Integer historyWeekSpanNums,
            List<String> customerTabTypeList){
        //  1、获取历史指定时间区间范围的节假周信息
        LocalDate date = LocalDate.parse(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(date, historyWeekSpanNums);
        if (ListUtils.isEmpty(holidayWeekInfo)) {
            return Lang.list();
        }

        //  获取历史十三周每周的周末日期
        List<String> statTimes = ListUtils.transform(holidayWeekInfo, o -> o.getEndDate());
        //  获取一个Map，其中k是年@周，v是节假信息
        Map<String, HolidayWeekInfoDTO> yearWeekMap = ListUtils.toMap(holidayWeekInfo, o -> o.getYearWeek(), Function.identity());

        //  2、获取数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/history_week_diff_data.sql");
        HashMap<String, Object> params = new HashMap<>();
        params.put("statTimes", statTimes);
        //  目前只要CVM的
        params.put("product", "CVM");
        params.put("customerTabTypeList", customerTabTypeList);

        //  针对uin的逻辑
        if (ListUtils.isNotEmpty(uins)){
            if (isPositive != null && isPositive){      //  正向，过滤uins
                sql = sql.replace("${UIN_CONDITION}", "and uin in (:uins)");
                params.put("uins", uins);
            } else if (isPositive != null && !isPositive) {     //  反向，剔除uins
                sql = sql.replace("${UIN_CONDITION}", "and uin not in (:uins)");
                params.put("uins", uins);
            } else {    //  不选无效
                sql = sql.replace("${UIN_CONDITION}", "");
            }
        } else {    //  uin为空无效
            sql = sql.replace("${UIN_CONDITION}", "");
        }

        //  针对客户名称/客户简称的逻辑
        if (ListUtils.isNotEmpty(customerNames)){
            if (isPositive != null && isPositive){      //  正向，过滤客户名称/简称
                sql = sql.replace("${CUSTOMER_NAME_CONDITION}",
                        "and (customer_name in (:customerNames) or customer_short_name in (:customerNames))");
                params.put("customerNames", customerNames);
            } else if (isPositive != null && !isPositive) {     //  反向，剔除客户名称/简称
                sql = sql.replace("${CUSTOMER_NAME_CONDITION}",
                        "and (customer_name not in (:customerNames) and customer_short_name not in (:customerNames))");
                params.put("customerNames", customerNames);
            } else {    //  不选无效
                sql = sql.replace("${CUSTOMER_NAME_CONDITION}", "");
            }
        } else {    //  客户简称列表为空无效
            sql = sql.replace("${CUSTOMER_NAME_CONDITION}", "");
        }
        //  3、转换生成DO
        List<SafetyInventoryHistoryDTO> raw = ckcldStdCrpDBHelper.getRaw(SafetyInventoryHistoryDTO.class, sql, params);
        List<DwsInventoryHealthWeeklyScaleDfDO> result = Lang.list();
        ListUtils.forEach(raw, o -> {
            HolidayWeekInfoDTO dto = yearWeekMap.get(o.getYearWeek());
            if (dto != null) {
                DwsInventoryHealthWeeklyScaleDfDO one = SafetyInventoryHistoryDTO.transform(o, dto);
                one.setWeekDiffLogicNum(o.getLogicNum());
                result.add(one);
            }
        });
        return result;

    }

    /**
     * 未来-周峰安全库存底数生成
     */
    private List<DwsInventoryHealthWeeklyScaleDfDO> genFutureWeekPeakData(String statTime, Integer futureWeekSpanNums) {
        List<DwsInventoryHealthWeeklyScaleDfDO> futureForecastData = Lang.list();
        //  中长尾客户
        futureForecastData.addAll(genFutureMediumLongTailPartData(statTime, futureWeekSpanNums));
        //  头部-战略客户部客户(这部分直接配置了安全库存结果，没有中间预测量)
        futureForecastData.addAll(genFutureForecastZlkhbPartData(statTime, futureWeekSpanNums));
        //  头部-非战略客户部客户
        futureForecastData.addAll(genFutureForecastOtherPartData(statTime, futureWeekSpanNums));
        return futureForecastData;
    }


    /**
     * 未来预测-头部战略客户部 部分数据
     */
    private List<DwsInventoryHealthWeeklyScaleDfDO> genFutureForecastZlkhbPartData(String statTime, Integer futureWeekSpanNums){
        //  1、获取未来13周的时间范围
        LocalDate date = LocalDate.parse(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(date, futureWeekSpanNums);

        ResPlanHolidayWeekDO curWeek = baseDictService.getHolidayWeekInfoByDate(statTime);
        HolidayWeekInfoDTO curWeekDTO = new HolidayWeekInfoDTO();
        curWeekDTO.setYear(curWeek.getYear());
        curWeekDTO.setMonth(curWeek.getMonth());
        curWeekDTO.setWeek(curWeek.getWeek());
        curWeekDTO.setStartDate(curWeek.getStart());
        curWeekDTO.setEndDate(curWeek.getEnd());
        curWeekDTO.setWeekNFromNow(0);
        holidayWeekInfo.add(curWeekDTO);

        if (ListUtils.isEmpty(holidayWeekInfo)) {
            return Lang.list();
        }

        List<Object[]> yearWeekList = Lang.list();
        for (HolidayWeekInfoDTO dto : holidayWeekInfo) {
            yearWeekList.add(new Object[]{dto.getYear(), dto.getWeek()});
        }

        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/future_week_peak_zlkhb_data.sql");
        HashMap<String, Object> params = new HashMap<>();
        params.put("statTime", statTime);
        List<SafetyInventoryHistoryDTO> raw = demandDBHelper.getRaw(SafetyInventoryHistoryDTO.class, sql, params);

        List<DwsInventoryHealthWeeklyScaleDfDO> result = Lang.list();
        for (HolidayWeekInfoDTO dto : holidayWeekInfo) {
            ListUtils.forEach(raw, o -> {
                DwsInventoryHealthWeeklyScaleDfDO one = SafetyInventoryHistoryDTO.transform(o, dto);
                one.setCustomerCustomGroup(CustomerCustomGroupEnum.HEAD_ZLKHB.getName());
                one.setStatTime(date);
                one.setWeekPeakLogicNum(o.getLogicNum());
                result.add(one);
            });
        }

        return result;
    }

    /**
     * 未来预测-头部非战略客户部 部分数据
     */
    private List<DwsInventoryHealthWeeklyScaleDfDO> genFutureForecastOtherPartData(String statTime, Integer futureWeekSpanNums){

        // 1、获取未来13周的时间范围
        LocalDate date = LocalDate.parse(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(date, futureWeekSpanNums);

        ResPlanHolidayWeekDO curWeek = baseDictService.getHolidayWeekInfoByDate(statTime);
        String startDate = SpecialDateUtils.getCurMonthFirstDate(curWeek.getYear(), curWeek.getMonth());
        HolidayWeekInfoDTO curWeekDTO = new HolidayWeekInfoDTO();
        curWeekDTO.setYear(curWeek.getYear());
        curWeekDTO.setMonth(curWeek.getMonth());
        curWeekDTO.setWeek(curWeek.getWeek());
        curWeekDTO.setStartDate(curWeek.getStart());
        curWeekDTO.setEndDate(curWeek.getEnd());
        curWeekDTO.setWeekNFromNow(0);
        holidayWeekInfo.add(curWeekDTO);

        if (ListUtils.isEmpty(holidayWeekInfo)) {
            return Lang.list();
        }

        ListUtils.sortAscNullLast(holidayWeekInfo, o -> o.getWeek());
        HolidayWeekInfoDTO lastOne = holidayWeekInfo.get(holidayWeekInfo.size() - 1);
        String endDate = SpecialDateUtils.getCurMonthLastDate(lastOne.getYear(), lastOne.getMonth());


        //  2、获取数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/future_week_peak_not_zlkhb_data.sql");
        Map<String, Object> params = new HashMap<>();
        //  框定时间范围：startDate当前周即第0周的开始时间
        params.put("startDate",  startDate);
        //  框定时间范围：endDate为第13周的结束时间
        params.put("endDate", endDate);
        List<SafetyInventoryFutureOtherDTO> raw = DBList.ckcldStdCrpDBHelper.getRaw(SafetyInventoryFutureOtherDTO.class, sql, params);

        //  3、基于赢率算需求
        ListUtils.forEach(raw, o -> {
            Integer year = DateUtils.getYear(o.getBeginBuyDate());
            Integer month = DateUtils.getMonth(o.getBeginBuyDate());
            //  赢率是否小于50%
            boolean lessThanHalf =  o.getWinRate().compareTo(BigDecimal.valueOf(50)) < 0;
            //  赢率小于50%，需求量减半，反之不变
            o.setResult(lessThanHalf ? AmountUtils.multiply(BigDecimal.valueOf(0.5), o.getLogicNum()) : o.getLogicNum());
            o.setYear(year);
            o.setMonth(month);
            o.setProduct("CVM");
        });

        //  4、根据年月、地域四维度、实例类型合并数据
        Map<String, List<SafetyInventoryFutureOtherDTO>> yearMonthMap = ListUtils.groupBy(raw, o -> o.getKey());
        List<TxyRegionInfoDTO> allTxyRegionInfo = baseDictService.getAllTxyRegionInfo();
        Map<String, TxyRegionInfoDTO> zoneNameMap
                = ListUtils.toMap(allTxyRegionInfo, o -> o.getZoneName(), Function.identity());

        //  5、数据清洗
        List<SafetyInventoryFutureOtherDTO> mergeRaw = Lang.list();
        for (Map.Entry<String, List<SafetyInventoryFutureOtherDTO>> entry : yearMonthMap.entrySet()) {
            SafetyInventoryFutureOtherDTO anyone = entry.getValue().get(0);
            SafetyInventoryFutureOtherDTO newOne = new SafetyInventoryFutureOtherDTO();
            newOne.setYear(anyone.getYear());
            newOne.setMonth(anyone.getMonth());
            newOne.setProduct(anyone.getProduct());
            newOne.setRegionName(anyone.getRegionName());
            newOne.setZoneName(anyone.getZoneName());
            newOne.setInstanceType(anyone.getInstanceType());
            //  获取当前年-月对应的节假周周数
            newOne.setWeekCount(baseDictService.getHolidayWeekInfoByYearMonth(anyone.getYear(), anyone.getMonth()).size());
            newOne.setResult(NumberUtils.sum(entry.getValue(), o -> o.getResult()));
            //  月度需求拆周
            newOne.setWeekResult(AmountUtils.divideScale6(newOne.getResult(), BigDecimal.valueOf(newOne.getWeekCount())));
            //  关联地域信息
            TxyRegionInfoDTO zoneInfo = zoneNameMap.get(anyone.getZoneName());
            if (zoneInfo != null){
                newOne.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                newOne.setAreaName(zoneInfo.getAreaName());
            } else {
                //  当可用区为随机可用区且地域不为空时，先去表里拿默认可用区，再重新获取一次，再没有就不填了
                if (Objects.equals(anyone.getZoneName(), "随机可用区") && StringTools.isNotBlank(anyone.getRegionName())){
                    String zoneName = baseDictService.queryDefaultZoneNameByRegionName(anyone.getRegionName());
                    if (StringTools.isNotBlank(zoneName)){
                        zoneInfo = zoneNameMap.get(anyone.getZoneName());
                        if (zoneInfo != null) {
                            newOne.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                            newOne.setAreaName(zoneInfo.getAreaName());
                        }
                    }
                }
            }
            mergeRaw.add(newOne);
        }

        //  6、数据生成
        List<DwsInventoryHealthWeeklyScaleDfDO> result = Lang.list();

        for (HolidayWeekInfoDTO dto : holidayWeekInfo) {
            String yearMonth = Strings.join("@", dto.getYear(), dto.getMonth());
            List<SafetyInventoryFutureOtherDTO> filter =
                    ListUtils.filter(mergeRaw, o -> Objects.equals(yearMonth, o.getYearMonth()));
            ListUtils.forEach(filter, o -> {
                DwsInventoryHealthWeeklyScaleDfDO one = SafetyInventoryFutureOtherDTO.transform(o, dto);
                one.setCustomerCustomGroup(CustomerCustomGroupEnum.HEAD_NOT_ZLKHB.getName());
                one.setStatTime(date);
                one.setWeekPeakLogicNum(o.getWeekResult());
                result.add(one);
            });
        }

        log.info("未来预测算法-头部 非战略客户部客户数据生成,共{}条", result.size());
        return result;
    }



    /**
     * 未来预测-中长尾 部分数据
     */
    private List<DwsInventoryHealthWeeklyScaleDfDO> genFutureMediumLongTailPartData(String statTime, Integer futureWeekSpanNums) {
        //  1、获取未来13周的时间范围
        LocalDate date = LocalDate.parse(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(date, futureWeekSpanNums);

        ResPlanHolidayWeekDO curWeek = baseDictService.getHolidayWeekInfoByDate(statTime);
        HolidayWeekInfoDTO curWeekDTO = new HolidayWeekInfoDTO();
        curWeekDTO.setYear(curWeek.getYear());
        curWeekDTO.setMonth(curWeek.getMonth());
        curWeekDTO.setWeek(curWeek.getWeek());
        curWeekDTO.setStartDate(curWeek.getStart());
        curWeekDTO.setEndDate(curWeek.getEnd());
        curWeekDTO.setWeekNFromNow(0);
        holidayWeekInfo.add(curWeekDTO);

        if (ListUtils.isEmpty(holidayWeekInfo)) {
            return Lang.list();
        }

        List<Object[]> yearWeekList = Lang.list();
        for (HolidayWeekInfoDTO dto : holidayWeekInfo) {
            yearWeekList.add(new Object[]{dto.getYear(), dto.getWeek()});
        }

        //  2、查询DB
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/future_week_peak_mlt_data.sql");
        HashMap<String, Object> params = new HashMap<>();
        params.put("yearWeekList", yearWeekList);
        List<SafetyInventoryHistoryDTO> raw = demandDBHelper.getRaw(SafetyInventoryHistoryDTO.class, sql, params);

        //  3、获取关联信息
        //  获取一个Map，其中k是当周第一天，v是当周对statTime是第几周
        Map<String, HolidayWeekInfoDTO> yearWeekMap = ListUtils.toMap(holidayWeekInfo, o -> o.getYearWeek(), Function.identity());
        List<TxyRegionInfoDTO> allTxyRegionInfo = baseDictService.getAllTxyRegionInfo();
        Map<String, TxyRegionInfoDTO> zoneNameMap
                = ListUtils.toMap(allTxyRegionInfo, o -> o.getZoneName(), Function.identity());

        //  4、转换为DO
        List<DwsInventoryHealthWeeklyScaleDfDO> result = Lang.list();
        ListUtils.forEach(raw, o -> {
            //  关联年月信息
            HolidayWeekInfoDTO dto = yearWeekMap.get(o.getYearWeek());
            //  关联地域信息
            TxyRegionInfoDTO zoneInfo = zoneNameMap.get(o.getZoneName());
            if (zoneInfo != null){
                o.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                o.setAreaName(zoneInfo.getAreaName());
            } else {
                //  当可用区为随机可用区且地域不为空时，先去表里拿默认可用区，再重新获取一次，再没有就不填了
                if (Objects.equals(o.getZoneName(), "随机可用区") && StringTools.isNotBlank(o.getRegionName())){
                    String zoneName = baseDictService.queryDefaultZoneNameByRegionName(o.getRegionName());
                    if (StringTools.isNotBlank(zoneName)){
                        zoneInfo = zoneNameMap.get(o.getZoneName());
                        if (zoneInfo != null) {
                            o.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                            o.setAreaName(zoneInfo.getAreaName());
                        }
                    }
                }
            }

            if (dto != null) {
                DwsInventoryHealthWeeklyScaleDfDO one = SafetyInventoryHistoryDTO.transform(o, dto);
                one.setCustomerCustomGroup("中长尾客户");
                one.setStatTime(LocalDate.parse(statTime));
                one.setWeekPeakLogicNum(o.getLogicNum());
                result.add(one);
            }
        });
        log.info("未来预测算法-中长尾客户数据生成,共{}条", result.size());
        return result;
    }

}
