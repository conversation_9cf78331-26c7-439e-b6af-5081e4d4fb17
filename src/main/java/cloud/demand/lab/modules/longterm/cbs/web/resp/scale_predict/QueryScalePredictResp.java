package cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict;

import cloud.demand.lab.modules.longterm.cbs.dto.CbsLongtermPredictScaleDTO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class QueryScalePredictResp {
    List<PredictStrategyTypeItem> predictStrategyTypeItems=new ArrayList<>();
   // List<CbsLongtermPredictScaleDTO> cbsPredictIncreaseDTOS=new ArrayList<>();
    @Data
    public static class PredictStrategyTypeItem{
        private String strategyType;
        private List<Item> items=new ArrayList<>();
        private List<IncreaseRate> increaseRates=new ArrayList<>();
    }

    @Data
    public static class Item{
        private BigDecimal sumDisk;
        private String dimsName;
        private String yearMonth;
        private BigDecimal rate;
    }

    //半年的增速
    @Data
    public static class IncreaseRate{
        private String startYearMonth;
        private String endYearMonth;
        private BigDecimal increaseRate;
        //上下半年展示名称
        private String dateName;

        public IncreaseRate(String halfYearName, String format, String format1, BigDecimal rate) {
            this.dateName = halfYearName;
            this.startYearMonth = format;
            this.endYearMonth = format1;
            this.increaseRate = rate;
        }
    }

}
