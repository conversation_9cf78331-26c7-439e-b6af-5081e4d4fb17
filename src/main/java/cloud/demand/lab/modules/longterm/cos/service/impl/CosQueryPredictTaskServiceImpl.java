package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.service.CosQueryPredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgsDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskInfoReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskInputArgsReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskPredictResultSummaryReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskInfoResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskInputArgsResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskPredictResultSummaryResp;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.stereotype.Component;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class CosQueryPredictTaskServiceImpl implements CosQueryPredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req) {
        // 查询所有启用的COS预测任务
        List<CosLongtermPredictTaskDO> tasks = cdLabDbHelper.getAll(CosLongtermPredictTaskDO.class, "where is_enable=1");

        // 如果还没有任何cos预测任务，那么只列cos方案
        if (ListUtils.isEmpty(tasks)) {
            QueryCategoryAndTaskListResp resp = new QueryCategoryAndTaskListResp();
            resp.setCategoryList(new ArrayList<>());
            List<CosLongtermPredictCategoryConfigDO> categories = cdLabDbHelper.getAll(CosLongtermPredictCategoryConfigDO.class);
            for (CosLongtermPredictCategoryConfigDO category : categories) {
                QueryCategoryAndTaskListResp.Category categoryResp = new QueryCategoryAndTaskListResp.Category();
                categoryResp.setCategoryId(category.getId());
                categoryResp.setCategoryName(category.getCategory());
                categoryResp.setModelPart(category.getModelPart());
                categoryResp.setPredictTaskList(new ArrayList<>());
                resp.getCategoryList().add(categoryResp);
            }
            return resp;
        }

        // 按方案ID分组
        Map<Long, List<CosLongtermPredictTaskDO>> categoryList = ListUtils.toMapList(tasks, o -> o.getCategoryId(), o -> o);

        QueryCategoryAndTaskListResp resp = new QueryCategoryAndTaskListResp();
        List<QueryCategoryAndTaskListResp.Category> categoryResult = new ArrayList<>();
        resp.setCategoryList(categoryResult);

        for (List<CosLongtermPredictTaskDO> t : categoryList.values()) {
            // 按预测开始时间倒序排列
            ListUtils.sortDescNullLast(t, CosLongtermPredictTaskDO::getPredictStart);

            QueryCategoryAndTaskListResp.Category category = new QueryCategoryAndTaskListResp.Category();
            category.setCategoryId(t.get(0).getCategoryId());
            category.setCategoryName(t.get(0).getCategoryName());
            category.setModelPart(t.get(0).getModelPart());

            List<QueryCategoryAndTaskListResp.Task> taskList = ListUtils.transform(t, o -> {
                QueryCategoryAndTaskListResp.Task task = new QueryCategoryAndTaskListResp.Task();
                task.setTaskId(o.getId());
                task.setYearMonth(DateUtils.format(o.getPredictStart(), "yyyy-MM"));
                task.setTaskStatusCode(o.getTaskStatus());
                task.setTaskStatusName(LongtermPredictTaskStatusEnum.getNameByCode(o.getTaskStatus()));
                return task;
            });
            category.setPredictTaskList(taskList);
            categoryResult.add(category);
        }

        // 按方案ID升序排列
        ListUtils.sortAscNullLast(categoryResult, QueryCategoryAndTaskListResp.Category::getCategoryId);
        return resp;
    }

    @Override
    public QueryTaskInfoResp queryTaskInfo(QueryTaskInfoReq req) {
        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, req.getTaskId());
        if (taskDO == null) {
            throw new BizException("预测任务不存在");
        }

        QueryTaskInfoResp resp = new QueryTaskInfoResp();
        resp.setCreateTime(DateUtils.format(taskDO.getCreateTime()));
        resp.setPredictStart(taskDO.getPredictStart() != null ? taskDO.getPredictStart().toString() : null);
        resp.setPredictEnd(taskDO.getPredictEnd() != null ? taskDO.getPredictEnd().toString() : null);
        resp.setDimsName(taskDO.getDimsName());
        resp.setScopeCustomer(taskDO.getScopeCustomer());
        resp.setScopeResourcePool(taskDO.getScopeResourcePool());

        String parts = taskDO.getParts();
        String[] partsArr = parts.split(",");

        resp.setParts(StringTools.join(partsArr, "+"));
        resp.setModelPart(taskDO.getModelPart());

        // 处理出计算公式
        StringBuilder formula = new StringBuilder("采购量=");

        for (String part : partsArr) {
            if (!part.equals(taskDO.getModelPart())) {
                formula.append(part).append("净增+");
            }
        }
        formula.append(taskDO.getModelPart()).append("[");
        formula.append("∑每半年(外部期初存量*外部半年增速% + 内部期初存量*内部半年增速%)");
        formula.append("]");
        resp.setModelFormula(formula.toString());
        return resp;
    }

    @Override
    public QueryTaskInputArgsResp queryLastInputArgs(QueryTaskInputArgsReq req) {
        QueryTaskInputArgsResp resp = new QueryTaskInputArgsResp();
        resp.setInputArgs(new ArrayList<>());

        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, req.getTaskId());
        if (taskDO == null) {
            throw new BizException("预测任务不存在");
        }

        // 2. 查询该任务的输入参数
        List<CosLongtermPredictInputArgsDO> inputArgsDOList = cdLabDbHelper.getAll(
                CosLongtermPredictInputArgsDO.class, "where task_id=? order by start_date", taskDO.getId());

        // 查询该策略类型的大客户未来预测数据
        List<CosLongtermPredictOutBigCustomerChangeDO> bigCustomerForecastDOList =
                cdLabDbHelper.getAll(CosLongtermPredictOutBigCustomerChangeDO.class, "where task_id=?", taskDO.getId());

        // 按策略类型对大客户预测数据进行分组
        Map<String, List<CosLongtermPredictOutBigCustomerChangeDO>> bigCustomerByStrategyType =
                ListUtils.toMapList(bigCustomerForecastDOList, CosLongtermPredictOutBigCustomerChangeDO::getStrategyType, o -> o);

        // 3. 转换DO为DTO并查询大客户未来预测数据
        List<InputArgsDTO> inputArgsDTOList = new ArrayList<>();
        Set<Long> matchedBigCustomerList = new HashSet<>();

        for (CosLongtermPredictInputArgsDO inputArgsDO : inputArgsDOList) {
            InputArgsDTO inputArgsDTO = InputArgsDTO.fromDO(inputArgsDO);

            // 对于未来大客户预测量，要按策略类型+时间范围放到对应的inputArgs参数中
            List<BigCustomerChangeDTO> bigCustomerForecast = new ArrayList<>();

            // 获取当前策略类型的大客户预测数据
            List<CosLongtermPredictOutBigCustomerChangeDO> strategyBigCustomerList =
                    bigCustomerByStrategyType.get(inputArgsDO.getStrategyType());

            if (ListUtils.isNotEmpty(strategyBigCustomerList)) {
                for (CosLongtermPredictOutBigCustomerChangeDO bigCustomerDO : strategyBigCustomerList) {
                    // 检查时间范围是否重叠
                    if (isDateRangeOverlap(inputArgsDO.getStartDate(), inputArgsDO.getEndDate(),
                            bigCustomerDO.getStartDate(), bigCustomerDO.getEndDate())) {
                        bigCustomerForecast.add(BigCustomerChangeDTO.fromDO(bigCustomerDO));
                        matchedBigCustomerList.add(bigCustomerDO.getId());
                    }
                }
            }

            inputArgsDTO.setBigCustomerForecast(bigCustomerForecast);
            inputArgsDTOList.add(inputArgsDTO);
        }

        // 如果还有bigCustomerForecastDOList中的元素对应不上inputArgs的时间范围，那么则放到对应的策略类型上的第一个inputArgs里
        List<CosLongtermPredictOutBigCustomerChangeDO> unmatchedBigCustomerList = new ArrayList<>();
        for (CosLongtermPredictOutBigCustomerChangeDO bigCustomerDO : bigCustomerForecastDOList) {
            if (!matchedBigCustomerList.contains(bigCustomerDO.getId())) {
                unmatchedBigCustomerList.add(bigCustomerDO);
            }
        }

        // 将未匹配的大客户预测数据按策略类型分配到第一个对应的inputArgs中
        if (ListUtils.isNotEmpty(unmatchedBigCustomerList)) {
            Map<String, List<CosLongtermPredictOutBigCustomerChangeDO>> unmatchedByStrategyType =
                    ListUtils.toMapList(unmatchedBigCustomerList, CosLongtermPredictOutBigCustomerChangeDO::getStrategyType, o -> o);

            for (InputArgsDTO inputArgsDTO : inputArgsDTOList) {
                List<CosLongtermPredictOutBigCustomerChangeDO> unmatchedForStrategy =
                        unmatchedByStrategyType.get(inputArgsDTO.getStrategyType());

                if (ListUtils.isNotEmpty(unmatchedForStrategy)) {
                    // 将未匹配的数据添加到当前inputArgs的bigCustomerForecast中
                    List<BigCustomerChangeDTO> currentForecast = inputArgsDTO.getBigCustomerForecast();
                    if (currentForecast == null) {
                        currentForecast = new ArrayList<>();
                        inputArgsDTO.setBigCustomerForecast(currentForecast);
                    }

                    for (CosLongtermPredictOutBigCustomerChangeDO unmatchedDO : unmatchedForStrategy) {
                        currentForecast.add(BigCustomerChangeDTO.fromDO(unmatchedDO));
                    }

                    // 清空该策略类型的未匹配数据，确保只分配给第一个inputArgs
                    unmatchedByStrategyType.remove(inputArgsDTO.getStrategyType());
                }
            }
        }

        resp.setInputArgs(inputArgsDTOList);
        return resp;
    }

    /**
     * 判断两个时间范围是否重叠
     * @param start1 第一个时间范围的开始时间
     * @param end1 第一个时间范围的结束时间
     * @param start2 第二个时间范围的开始时间
     * @param end2 第二个时间范围的结束时间
     * @return 如果时间范围重叠返回true，否则返回false
     */
    private boolean isDateRangeOverlap(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        if (start1 == null || end1 == null || start2 == null || end2 == null) {
            return false;
        }
        // 两个时间范围重叠的条件：start1 <= end2 && start2 <= end1
        return !start1.isAfter(end2) && !start2.isAfter(end1);
    }

    @Override
    public QueryTaskPredictResultSummaryResp queryTaskPredictResultSummary(QueryTaskPredictResultSummaryReq req) {

        return null;
    }
}
