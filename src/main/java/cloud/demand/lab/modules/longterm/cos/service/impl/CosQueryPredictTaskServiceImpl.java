package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.service.CosQueryPredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgsDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskInfoReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskInputArgsReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskPredictResultSummaryReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskInfoResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskInputArgsResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskPredictResultSummaryResp;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.stereotype.Component;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

@Component
public class CosQueryPredictTaskServiceImpl implements CosQueryPredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req) {
        // 查询所有启用的COS预测任务
        List<CosLongtermPredictTaskDO> tasks = cdLabDbHelper.getAll(CosLongtermPredictTaskDO.class, "where is_enable=1");

        // 如果还没有任何cos预测任务，那么只列cos方案
        if (ListUtils.isEmpty(tasks)) {
            QueryCategoryAndTaskListResp resp = new QueryCategoryAndTaskListResp();
            resp.setCategoryList(new ArrayList<>());
            List<CosLongtermPredictCategoryConfigDO> categories = cdLabDbHelper.getAll(CosLongtermPredictCategoryConfigDO.class);
            for (CosLongtermPredictCategoryConfigDO category : categories) {
                QueryCategoryAndTaskListResp.Category categoryResp = new QueryCategoryAndTaskListResp.Category();
                categoryResp.setCategoryId(category.getId());
                categoryResp.setCategoryName(category.getCategory());
                categoryResp.setModelPart(category.getModelPart());
                categoryResp.setPredictTaskList(new ArrayList<>());
                resp.getCategoryList().add(categoryResp);
            }
            return resp;
        }

        // 按方案ID分组
        Map<Long, List<CosLongtermPredictTaskDO>> categoryList = ListUtils.toMapList(tasks, o -> o.getCategoryId(), o -> o);

        QueryCategoryAndTaskListResp resp = new QueryCategoryAndTaskListResp();
        List<QueryCategoryAndTaskListResp.Category> categoryResult = new ArrayList<>();
        resp.setCategoryList(categoryResult);

        for (List<CosLongtermPredictTaskDO> t : categoryList.values()) {
            // 按预测开始时间倒序排列
            ListUtils.sortDescNullLast(t, CosLongtermPredictTaskDO::getPredictStart);

            QueryCategoryAndTaskListResp.Category category = new QueryCategoryAndTaskListResp.Category();
            category.setCategoryId(t.get(0).getCategoryId());
            category.setCategoryName(t.get(0).getCategoryName());
            category.setModelPart(t.get(0).getModelPart());

            List<QueryCategoryAndTaskListResp.Task> taskList = ListUtils.transform(t, o -> {
                QueryCategoryAndTaskListResp.Task task = new QueryCategoryAndTaskListResp.Task();
                task.setTaskId(o.getId());
                task.setYearMonth(DateUtils.format(o.getPredictStart(), "yyyy-MM"));
                task.setTaskStatusCode(o.getTaskStatus());
                task.setTaskStatusName(LongtermPredictTaskStatusEnum.getNameByCode(o.getTaskStatus()));
                return task;
            });
            category.setPredictTaskList(taskList);
            categoryResult.add(category);
        }

        // 按方案ID升序排列
        ListUtils.sortAscNullLast(categoryResult, QueryCategoryAndTaskListResp.Category::getCategoryId);
        return resp;
    }

    @Override
    public QueryTaskInfoResp queryTaskInfo(QueryTaskInfoReq req) {
        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, req.getTaskId());
        if (taskDO == null) {
            throw new BizException("预测任务不存在");
        }

        QueryTaskInfoResp resp = new QueryTaskInfoResp();
        resp.setCreateTime(DateUtils.format(taskDO.getCreateTime()));
        resp.setPredictStart(taskDO.getPredictStart() != null ? taskDO.getPredictStart().toString() : null);
        resp.setPredictEnd(taskDO.getPredictEnd() != null ? taskDO.getPredictEnd().toString() : null);
        resp.setDimsName(taskDO.getDimsName());
        resp.setScopeCustomer(taskDO.getScopeCustomer());
        resp.setScopeResourcePool(taskDO.getScopeResourcePool());

        String parts = taskDO.getParts();
        String[] partsArr = parts.split(",");

        resp.setParts(StringTools.join(partsArr, "+"));
        resp.setModelPart(taskDO.getModelPart());

        // 处理出计算公式
        StringBuilder formula = new StringBuilder("采购量=");

        for (String part : partsArr) {
            if (!part.equals(taskDO.getModelPart())) {
                formula.append(part).append("净增+");
            }
        }
        formula.append(taskDO.getModelPart()).append("[");
        formula.append("∑每半年(外部期初存量*外部半年增速% + 内部期初存量*内部半年增速%)");
        formula.append("]");
        resp.setModelFormula(formula.toString());
        return resp;
    }

    @Override
    public QueryTaskInputArgsResp queryLastInputArgs(QueryTaskInputArgsReq req) {
        QueryTaskInputArgsResp resp = new QueryTaskInputArgsResp();
        resp.setInputArgs(new ArrayList<>());

        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, req.getTaskId());
        if (taskDO == null) {
            throw new BizException("预测任务不存在");
        }

        // 2. 查询该任务的输入参数
        List<CosLongtermPredictInputArgsDO> inputArgsDOList = cdLabDbHelper.getAll(
                CosLongtermPredictInputArgsDO.class, "where task_id=? order by start_date", taskDO.getId());

        // 查询该策略类型的大客户未来预测数据
        List<CosLongtermPredictOutBigCustomerChangeDO> bigCustomerForecastDOList =
                cdLabDbHelper.getAll(CosLongtermPredictOutBigCustomerChangeDO.class, "where task_id=?", taskDO.getId());

        // 按策略类型对大客户预测数据进行分组
        Map<String, List<CosLongtermPredictOutBigCustomerChangeDO>> bigCustomerByStrategyType =
                ListUtils.toMapList(bigCustomerForecastDOList, CosLongtermPredictOutBigCustomerChangeDO::getStrategyType, o -> o);

        // 3. 转换DO为DTO并查询大客户未来预测数据
        List<InputArgsDTO> inputArgsDTOList = new ArrayList<>();
        Set<Long> matchedBigCustomerList = new HashSet<>();

        for (CosLongtermPredictInputArgsDO inputArgsDO : inputArgsDOList) {
            InputArgsDTO inputArgsDTO = InputArgsDTO.fromDO(inputArgsDO);

            // 对于未来大客户预测量，要按策略类型+时间范围放到对应的inputArgs参数中
            List<BigCustomerChangeDTO> bigCustomerForecast = new ArrayList<>();

            // 获取当前策略类型的大客户预测数据
            List<CosLongtermPredictOutBigCustomerChangeDO> strategyBigCustomerList =
                    bigCustomerByStrategyType.get(inputArgsDO.getStrategyType());

            if (ListUtils.isNotEmpty(strategyBigCustomerList)) {
                for (CosLongtermPredictOutBigCustomerChangeDO bigCustomerDO : strategyBigCustomerList) {
                    // 检查时间范围是否重叠
                    if (isDateRangeOverlap(inputArgsDO.getStartDate(), inputArgsDO.getEndDate(),
                            bigCustomerDO.getStartDate(), bigCustomerDO.getEndDate())) {
                        bigCustomerForecast.add(BigCustomerChangeDTO.fromDO(bigCustomerDO));
                        matchedBigCustomerList.add(bigCustomerDO.getId());
                    }
                }
            }

            inputArgsDTO.setBigCustomerForecast(bigCustomerForecast);
            inputArgsDTOList.add(inputArgsDTO);
        }

        // 如果还有bigCustomerForecastDOList中的元素对应不上inputArgs的时间范围，那么则放到对应的策略类型上的第一个inputArgs里
        List<CosLongtermPredictOutBigCustomerChangeDO> unmatchedBigCustomerList = new ArrayList<>();
        for (CosLongtermPredictOutBigCustomerChangeDO bigCustomerDO : bigCustomerForecastDOList) {
            if (!matchedBigCustomerList.contains(bigCustomerDO.getId())) {
                unmatchedBigCustomerList.add(bigCustomerDO);
            }
        }

        // 将未匹配的大客户预测数据按策略类型分配到第一个对应的inputArgs中
        if (ListUtils.isNotEmpty(unmatchedBigCustomerList)) {
            Map<String, List<CosLongtermPredictOutBigCustomerChangeDO>> unmatchedByStrategyType =
                    ListUtils.toMapList(unmatchedBigCustomerList, CosLongtermPredictOutBigCustomerChangeDO::getStrategyType, o -> o);

            for (InputArgsDTO inputArgsDTO : inputArgsDTOList) {
                List<CosLongtermPredictOutBigCustomerChangeDO> unmatchedForStrategy =
                        unmatchedByStrategyType.get(inputArgsDTO.getStrategyType());

                if (ListUtils.isNotEmpty(unmatchedForStrategy)) {
                    // 将未匹配的数据添加到当前inputArgs的bigCustomerForecast中
                    List<BigCustomerChangeDTO> currentForecast = inputArgsDTO.getBigCustomerForecast();
                    if (currentForecast == null) {
                        currentForecast = new ArrayList<>();
                        inputArgsDTO.setBigCustomerForecast(currentForecast);
                    }

                    for (CosLongtermPredictOutBigCustomerChangeDO unmatchedDO : unmatchedForStrategy) {
                        currentForecast.add(BigCustomerChangeDTO.fromDO(unmatchedDO));
                    }

                    // 清空该策略类型的未匹配数据，确保只分配给第一个inputArgs
                    unmatchedByStrategyType.remove(inputArgsDTO.getStrategyType());
                }
            }
        }

        resp.setInputArgs(inputArgsDTOList);
        return resp;
    }

    /**
     * 判断两个时间范围是否重叠
     * @param start1 第一个时间范围的开始时间
     * @param end1 第一个时间范围的结束时间
     * @param start2 第二个时间范围的开始时间
     * @param end2 第二个时间范围的结束时间
     * @return 如果时间范围重叠返回true，否则返回false
     */
    private boolean isDateRangeOverlap(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        if (start1 == null || end1 == null || start2 == null || end2 == null) {
            return false;
        }
        // 两个时间范围重叠的条件：start1 <= end2 && start2 <= end1
        return !start1.isAfter(end2) && !start2.isAfter(end1);
    }

    @Override
    public QueryTaskPredictResultSummaryResp queryTaskPredictResultSummary(QueryTaskPredictResultSummaryReq req) {
        // 1. 获取任务信息
        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, req.getTaskId());
        if (taskDO == null) {
            throw new BizException("预测任务不存在");
        }

        // 2. 确定覆盖的年份范围
        Set<Integer> coverYears = getCoverYears(taskDO.getPredictStart(), taskDO.getPredictEnd());

        // 3. 获取所有策略类型
        Set<String> strategyTypes = getStrategyTypes(req.getTaskId());

        // 4. 计算预测结果概览
        List<QueryTaskPredictResultSummaryResp.PredictSummary> summaryList = new ArrayList<>();

        for (String strategyType : strategyTypes) {
            for (Integer year : coverYears) {
                QueryTaskPredictResultSummaryResp.PredictSummary summary =
                    calculatePredictSummary(req.getTaskId(), strategyType, year);
                summaryList.add(summary);
            }
        }

        QueryTaskPredictResultSummaryResp resp = new QueryTaskPredictResultSummaryResp();
        resp.setPredictSummaryList(summaryList);
        return resp;
    }

    /**
     * 获取预测覆盖的年份范围
     */
    private Set<Integer> getCoverYears(LocalDate predictStart, LocalDate predictEnd) {
        Set<Integer> years = new TreeSet<>();
        if (predictStart != null && predictEnd != null) {
            int startYear = predictStart.getYear();
            int endYear = predictEnd.getYear();
            for (int year = startYear; year <= endYear; year++) {
                years.add(year);
            }
        }
        return years;
    }

    /**
     * 获取任务涉及的所有策略类型
     */
    private Set<String> getStrategyTypes(Long taskId) {
        Set<String> strategyTypes = new HashSet<>();

        // 从预测输出数据中获取策略类型
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_id = ?", taskId);

        List<CosLongtermPredictOutScaleDO> outScaleList = cdLabDbHelper.getAll(
                CosLongtermPredictOutScaleDO.class, whereSQL.getSQL(), whereSQL.getParams());

        for (CosLongtermPredictOutScaleDO outScale : outScaleList) {
            if (outScale.getStrategyType() != null) {
                strategyTypes.add(outScale.getStrategyType());
            }
        }

        // 从大客户变动数据中获取策略类型
        List<CosLongtermPredictOutBigCustomerChangeDO> bigCustomerList = cdLabDbHelper.getAll(
                CosLongtermPredictOutBigCustomerChangeDO.class, whereSQL.getSQL(), whereSQL.getParams());

        for (CosLongtermPredictOutBigCustomerChangeDO bigCustomer : bigCustomerList) {
            if (bigCustomer.getStrategyType() != null) {
                strategyTypes.add(bigCustomer.getStrategyType());
            }
        }

        return strategyTypes;
    }

    /**
     * 计算指定策略类型和年份的预测概览
     */
    private QueryTaskPredictResultSummaryResp.PredictSummary calculatePredictSummary(
            Long taskId, String strategyType, Integer year) {

        QueryTaskPredictResultSummaryResp.PredictSummary summary =
            new QueryTaskPredictResultSummaryResp.PredictSummary();
        summary.setStrategyType(strategyType);
        summary.setYear(year);

        List<QueryTaskPredictResultSummaryResp.PredictDetail> netChangePredictDetail = new ArrayList<>();

        // 1. 计算模型预测部分（内部+外部）
        BigDecimal modelPredictInternal = calculateModelPredict(taskId, strategyType, year, false);
        BigDecimal modelPredictExternal = calculateModelPredict(taskId, strategyType, year, true);

        if (modelPredictInternal != null && modelPredictInternal.compareTo(BigDecimal.ZERO) != 0) {
            QueryTaskPredictResultSummaryResp.PredictDetail detail =
                new QueryTaskPredictResultSummaryResp.PredictDetail();
            detail.setName("内部");
            detail.setValue(modelPredictInternal);
            netChangePredictDetail.add(detail);
        }

        if (modelPredictExternal != null && modelPredictExternal.compareTo(BigDecimal.ZERO) != 0) {
            QueryTaskPredictResultSummaryResp.PredictDetail detail =
                new QueryTaskPredictResultSummaryResp.PredictDetail();
            detail.setName("外部");
            detail.setValue(modelPredictExternal);
            netChangePredictDetail.add(detail);
        }

        // 2. 计算大客户部分
        BigDecimal bigCustomerChange = calculateBigCustomerChange(taskId, strategyType, year);
        if (bigCustomerChange != null && bigCustomerChange.compareTo(BigDecimal.ZERO) != 0) {
            QueryTaskPredictResultSummaryResp.PredictDetail detail =
                new QueryTaskPredictResultSummaryResp.PredictDetail();
            detail.setName("大客户");
            detail.setValue(bigCustomerChange);
            netChangePredictDetail.add(detail);
        }

        // 3. 计算总的净增预测量
        BigDecimal totalNetChange = BigDecimal.ZERO;
        if (modelPredictInternal != null) {
            totalNetChange = totalNetChange.add(modelPredictInternal);
        }
        if (modelPredictExternal != null) {
            totalNetChange = totalNetChange.add(modelPredictExternal);
        }
        if (bigCustomerChange != null) {
            totalNetChange = totalNetChange.add(bigCustomerChange);
        }

        summary.setNetChangePredict(totalNetChange);
        summary.setNetChangePredictDetail(netChangePredictDetail);

        // 4. 采购预测量和明细直接复制净增预测量
        summary.setPurchasePredict(totalNetChange);
        summary.setPurchasePredictDetail(new ArrayList<>(netChangePredictDetail));

        return summary;
    }

    /**
     * 计算模型预测部分的净增量
     * @param taskId 任务ID
     * @param strategyType 策略类型
     * @param year 年份
     * @param isOutCustomer 是否外部客户
     * @return 净增量
     */
    private BigDecimal calculateModelPredict(Long taskId, String strategyType, Integer year, Boolean isOutCustomer) {
        // 1. 获取该年份的预测数据
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_id = ?", taskId);
        whereSQL.and("strategy_type = ?", strategyType);
        whereSQL.and("is_out_customer = ?", isOutCustomer);
        whereSQL.and("YEAR(date) = ?", year);
        whereSQL.addOrderBy("date");

        List<CosLongtermPredictOutScaleDO> outScaleList = cdLabDbHelper.getAll(
                CosLongtermPredictOutScaleDO.class, whereSQL.getSQL(), whereSQL.getParams());

        if (ListUtils.isEmpty(outScaleList)) {
            return BigDecimal.ZERO;
        }

        // 2. 获取年初存量（用于计算净增量）
        BigDecimal yearStartScale = getYearStartScale(taskId, strategyType, year, isOutCustomer);

        // 3. 获取年末存量
        CosLongtermPredictOutScaleDO lastRecord = outScaleList.get(outScaleList.size() - 1);
        BigDecimal yearEndScale = lastRecord.getPredictScale();

        // 4. 计算净增量 = 年末存量 - 年初存量
        if (yearStartScale != null && yearEndScale != null) {
            return yearEndScale.subtract(yearStartScale);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取年初存量
     */
    private BigDecimal getYearStartScale(Long taskId, String strategyType, Integer year, Boolean isOutCustomer) {
        // 1. 先尝试从预测输出数据中获取年初存量
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_id = ?", taskId);
        whereSQL.and("strategy_type = ?", strategyType);
        whereSQL.and("is_out_customer = ?", isOutCustomer);
        whereSQL.and("date = ?", LocalDate.of(year, 1, 1));

        List<CosLongtermPredictOutScaleDO> outScaleList = cdLabDbHelper.getAll(
                CosLongtermPredictOutScaleDO.class, whereSQL.getSQL(), whereSQL.getParams());

        if (ListUtils.isNotEmpty(outScaleList)) {
            return outScaleList.get(0).getPredictScale();
        }

        // 2. 如果预测输出数据中没有，则从历史输入数据中获取
        WhereSQL inputWhereSQL = new WhereSQL();
        inputWhereSQL.and("task_id = ?", taskId);
        inputWhereSQL.and("is_out_customer = ?", isOutCustomer ? 1 : 0);
        inputWhereSQL.and("date <= ?", LocalDate.of(year, 1, 1));
        inputWhereSQL.addOrderBy("date desc"); // 降序，获取最近的历史数据

        List<CosLongtermPredictInputScaleDO> inputScaleList = cdLabDbHelper.getAll(
                CosLongtermPredictInputScaleDO.class, inputWhereSQL.getSQL(), inputWhereSQL.getParams());

        if (ListUtils.isNotEmpty(inputScaleList)) {
            return inputScaleList.get(0).getCurScale();
        }

        return BigDecimal.ZERO;
    }

    /**
     * 计算大客户变动部分
     */
    private BigDecimal calculateBigCustomerChange(Long taskId, String strategyType, Integer year) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_id = ?", taskId);
        whereSQL.and("strategy_type = ?", strategyType);

        // 查询时间范围与指定年份有重叠的大客户变动数据
        whereSQL.and("(YEAR(start_date) <= ? AND YEAR(end_date) >= ?)", year, year);

        List<CosLongtermPredictOutBigCustomerChangeDO> bigCustomerList = cdLabDbHelper.getAll(
                CosLongtermPredictOutBigCustomerChangeDO.class, whereSQL.getSQL(), whereSQL.getParams());

        BigDecimal totalChange = BigDecimal.ZERO;
        for (CosLongtermPredictOutBigCustomerChangeDO bigCustomer : bigCustomerList) {
            if (bigCustomer.getNetChange() != null) {
                // 计算该变动在指定年份的影响比例
                BigDecimal yearlyChange = calculateYearlyBigCustomerChange(bigCustomer, year);
                totalChange = totalChange.add(yearlyChange);
            }
        }

        return totalChange;
    }

    /**
     * 计算大客户变动在指定年份的影响
     */
    private BigDecimal calculateYearlyBigCustomerChange(CosLongtermPredictOutBigCustomerChangeDO bigCustomer, Integer year) {
        LocalDate startDate = bigCustomer.getStartDate();
        LocalDate endDate = bigCustomer.getEndDate();
        BigDecimal netChange = bigCustomer.getNetChange();

        if (startDate == null || endDate == null || netChange == null) {
            return BigDecimal.ZERO;
        }

        LocalDate yearStart = LocalDate.of(year, 1, 1);
        LocalDate yearEnd = LocalDate.of(year, 12, 31);

        // 计算重叠的时间范围
        LocalDate overlapStart = startDate.isAfter(yearStart) ? startDate : yearStart;
        LocalDate overlapEnd = endDate.isBefore(yearEnd) ? endDate : yearEnd;

        if (overlapStart.isAfter(overlapEnd)) {
            return BigDecimal.ZERO;
        }

        // 如果变动完全在该年份内，返回全部变动量
        if (!startDate.isBefore(yearStart) && !endDate.isAfter(yearEnd)) {
            return netChange;
        }

        // 如果变动跨越多个年份，按时间比例分配
        long totalDays = startDate.until(endDate).getDays() + 1;
        long overlapDays = overlapStart.until(overlapEnd).getDays() + 1;

        if (totalDays > 0) {
            BigDecimal ratio = BigDecimal.valueOf(overlapDays).divide(BigDecimal.valueOf(totalDays), 4, BigDecimal.ROUND_HALF_UP);
            return netChange.multiply(ratio);
        }

        return BigDecimal.ZERO;
    }
}
