package cloud.demand.lab.modules.longterm.cos.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryTaskPredictResultSummaryResp {

    @Data
    public static class PredictSummary {

        /**策略类型*/
        private String strategyType;

        /**年份，预测概览以年为单位*/
        private Integer year;

        /**净增预测量，单位pb*/
        private BigDecimal netChangePredict;

        /**采购预测量，单位pb*/
        private BigDecimal purchasePredict;

        /**净增预测量明细*/
        private List<PredictDetail> netChangePredictDetail;

        /**采购预测量明细*/
        private List<PredictDetail> purchasePredictDetail;

    }

    @Data
    public static class PredictDetail {

        private String name;

        private BigDecimal value;
    }

}
