select SUM(cur_service_disk) AS sum_disk,
       year_month
FROM std_crp.dwd_txy_cbs_scale_agg_df
WHERE stat_time IN (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), now()) + 1)
    ORDER BY last_day_of_month DESC
) and year_month >= '${startYearMonth}' and year_month<= '${endYearMonth}'
group by stat_time,year_month
order by year_month